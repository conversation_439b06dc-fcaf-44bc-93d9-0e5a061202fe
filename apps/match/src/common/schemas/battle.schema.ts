import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { HeroPosition } from '@libs/game-constants';

/**
 * 球员战斗信息
 * 基于old项目room.js的球员数据结构
 * 修复：使用项目统一的类型定义，确保架构一致性
 */
@Schema({ _id: false })
export class BattleHeroInfo {
  @Prop({ required: true })
  heroId: string;                        // 球员ID - 修复：使用统一的heroId命名

  @Prop({ default: 0 })
  attack: number;                        // 进攻值

  @Prop({ default: 0 })
  defend: number;                        // 防守值

  @Prop({ default: 0 })
  speed: number;                         // 速度值

  @Prop({ default: 0 })
  power: number;                         // 力量值

  @Prop({ default: 0 })
  technique: number;                     // 技术值

  @Prop({
    type: String,
    enum: Object.values(HeroPosition),
    required: true
  })
  position: HeroPosition;                // 位置 - 修复：使用统一的HeroPosition枚举

  @Prop({ default: 0 })
  level: number;                         // 等级

  @Prop({ type: [Number], default: [] })
  skills: number[];                      // 技能列表
}

/**
 * 队伍战斗数据
 * 基于old项目room.js的队伍数据结构
 */
@Schema({ _id: false })
export class BattleTeamData {
  @Prop({ required: true })
  characterId: string;                      // 玩家ID

  @Prop({ default: '' })
  teamName: string;                      // 队伍名称

  @Prop({ default: 0 })
  formation: number;                     // 阵型ID

  @Prop({ default: 0 })
  tactic: number;                        // 战术ID

  @Prop({ type: [BattleHeroInfo], default: [] })
  heroes: BattleHeroInfo[];              // 球员列表

  @Prop({ default: 0 })
  totalAttack: number;                   // 总进攻值

  @Prop({ default: 0 })
  totalDefend: number;                   // 总防守值

  @Prop({ default: 0 })
  morale: number;                        // 士气值

  @Prop({ default: 0 })
  score: number;                         // 比分
}

/**
 * 战斗回合信息
 * 基于old项目room.js的回合记录结构
 */
@Schema({ _id: false })
export class BattleRoundInfo {
  @Prop({ default: 0 })
  roundIndex: number;                    // 回合索引

  @Prop({ default: '' })
  attacker: string;                      // 进攻方 (teamA/teamB)

  @Prop({ default: 0 })
  attackMode: number;                    // 进攻方式

  @Prop({ default: 0 })
  battleTime: number;                    // 战斗时间

  @Prop({ default: 0 })
  scoreA: number;                        // A队比分

  @Prop({ default: 0 })
  scoreB: number;                        // B队比分

  @Prop({ type: Object, default: {} })
  periodInfo: any[];                     // 阶段信息（发起、推进、射门）
}

/**
 * 战斗结果数据
 * 基于old项目room.js的战斗结果结构
 */
@Schema({ _id: false })
export class BattleResult {
  @Prop({ required: true })
  roomUid: string;                       // 房间UID

  @Prop({ default: 0 })
  battleType: number;                    // 战斗类型

  @Prop({ default: 0 })
  homeScore: number;                     // 主队比分

  @Prop({ default: 0 })
  awayScore: number;                     // 客队比分

  @Prop({ default: 0 })
  winner: number;                        // 胜利方 (0平局 1主队 2客队)

  @Prop({ default: Date.now })
  battleTime: Date;                      // 战斗时间

  @Prop({ type: [BattleRoundInfo], default: [] })
  roundInfo: BattleRoundInfo[];          // 回合信息

  @Prop({ type: Object, default: {} })
  battleEndInfo: any;                    // 战斗结束信息

  @Prop({ type: Object, default: {} })
  skillRecord: any;                      // 技能记录
}

/**
 * 战斗房间主文档
 * 基于old项目room.js的房间数据结构
 */
@Schema({
  collection: 'battle_rooms',
  timestamps: true,
  versionKey: false,
})
export class BattleRoom extends Document {
  @Prop({ required: true, unique: true })
  roomUid: string;                       // 房间UID

  @Prop({ default: 0 })
  battleType: number;                    // 战斗类型

  @Prop({ default: 'active' })
  status: string;                        // 房间状态 (active/finished/expired)

  @Prop({ type: BattleTeamData })
  teamA: BattleTeamData;                 // A队数据

  @Prop({ type: BattleTeamData })
  teamB: BattleTeamData;                 // B队数据

  @Prop({ type: BattleResult })
  result: BattleResult;                  // 战斗结果

  @Prop({ default: Date.now })
  createdAt: Date;                       // 创建时间

  @Prop({ default: Date.now })
  finishedAt: Date;                      // 完成时间
}

export const BattleRoomSchema = SchemaFactory.createForClass(BattleRoom);

// 定义方法接口 - 基于BattleService的真实业务逻辑
export interface BattleRoomMethods {
  // 战斗状态管理 - 基于BattleService
  isActive(): boolean;
  isFinished(): boolean;
  canStart(): boolean;
  startBattle(): void;
  finishBattle(result: BattleResult): void;

  // 战斗数据管理 - 基于BattleService和BattleEngine
  getWinner(): number;
  getLoser(): number;
  isDraw(): boolean;
  getBattleDuration(): number;
  getTotalScore(): number;

  // 队伍数据管理 - 基于BattleService的队伍管理逻辑
  getTeamByCharacterId(characterId: string): BattleTeamData | null;
  getOpponentTeam(characterId: string): BattleTeamData | null;
  updateTeamScore(team: 'teamA' | 'teamB', score: number): void;
  updateTeamMorale(team: 'teamA' | 'teamB', morale: number): void;

  // 战斗统计 - 基于BattleEngine的统计逻辑
  getTotalRounds(): number;
  getGoalCount(): number;
  getAttackCount(): number;
  getTeamStats(team: 'teamA' | 'teamB'): any;

  // 战斗验证 - 基于BattleService的验证逻辑
  validateTeamData(): { isValid: boolean; errors: string[] };
  hasValidHeroes(): boolean;

  // 数据转换 - 基于BattleService的客户端数据需求
  toBattleReplay(): any;
  toBattleResult(): BattleResult;
  toClientBattleInfo(): any;
}

// 定义Document类型
export type BattleRoomDocument = BattleRoom & Document & BattleRoomMethods;

// 创建索引
BattleRoomSchema.index({ roomUid: 1 });
BattleRoomSchema.index({ status: 1 });
BattleRoomSchema.index({ battleType: 1 });
BattleRoomSchema.index({ createdAt: 1 });
BattleRoomSchema.index({ 'teamA.characterId': 1 });
BattleRoomSchema.index({ 'teamB.characterId': 1 });

// ==================== 实例方法实现 ====================

/**
 * 检查战斗是否激活
 * 基于BattleService: 战斗状态管理逻辑
 */
BattleRoomSchema.methods.isActive = function(): boolean {
  return this.status === 'active';
};

/**
 * 检查战斗是否完成
 * 基于BattleService: 战斗状态管理逻辑
 */
BattleRoomSchema.methods.isFinished = function(): boolean {
  return this.status === 'finished';
};

/**
 * 检查是否可以开始战斗
 * 基于BattleService: 战斗开始条件验证逻辑
 */
BattleRoomSchema.methods.canStart = function(): boolean {
  // 检查状态
  if (this.status !== 'active') return false;

  // 检查队伍数据
  if (!this.teamA || !this.teamB) return false;

  // 检查球员数据
  if (!this.teamA.heroes || this.teamA.heroes.length === 0) return false;
  if (!this.teamB.heroes || this.teamB.heroes.length === 0) return false;

  return true;
};

/**
 * 开始战斗
 * 基于BattleService: 战斗开始逻辑
 */
BattleRoomSchema.methods.startBattle = function(): void {
  if (this.canStart()) {
    this.status = 'active';
    // createdAt已经在创建时设置，这里不需要修改
  }
};

/**
 * 完成战斗
 * 基于BattleService: 战斗结束逻辑
 */
BattleRoomSchema.methods.finishBattle = function(result: BattleResult): void {
  this.status = 'finished';
  this.result = result;
  this.finishedAt = new Date();

  // 更新队伍比分
  this.teamA.score = result.homeScore;
  this.teamB.score = result.awayScore;
};

/**
 * 获取胜利方
 * 基于BattleService: determineWinner方法逻辑
 */
BattleRoomSchema.methods.getWinner = function(): number {
  if (!this.result) return 0;
  return this.result.winner;
};

/**
 * 获取失败方
 * 基于BattleService: 战斗结果判断逻辑
 */
BattleRoomSchema.methods.getLoser = function(): number {
  const winner = this.getWinner();
  if (winner === 0) return 0; // 平局
  return winner === 1 ? 2 : 1;
};

/**
 * 检查是否平局
 * 基于BattleService: 战斗结果判断逻辑
 */
BattleRoomSchema.methods.isDraw = function(): boolean {
  return this.getWinner() === 0;
};

/**
 * 获取战斗持续时间（毫秒）
 * 基于BattleService: 战斗时间统计逻辑
 */
BattleRoomSchema.methods.getBattleDuration = function(): number {
  if (!this.finishedAt || !this.createdAt) return 0;
  return this.finishedAt.getTime() - this.createdAt.getTime();
};

/**
 * 获取总比分
 * 基于BattleService: 比分统计逻辑
 */
BattleRoomSchema.methods.getTotalScore = function(): number {
  if (!this.result) return 0;
  return this.result.homeScore + this.result.awayScore;
};

/**
 * 根据角色ID获取队伍数据
 * 基于BattleService: 队伍数据查询逻辑
 */
BattleRoomSchema.methods.getTeamByCharacterId = function(characterId: string): BattleTeamData | null {
  if (this.teamA && this.teamA.characterId === characterId) {
    return this.teamA;
  }
  if (this.teamB && this.teamB.characterId === characterId) {
    return this.teamB;
  }
  return null;
};

/**
 * 获取对手队伍
 * 基于BattleService: 对手查询逻辑
 */
BattleRoomSchema.methods.getOpponentTeam = function(characterId: string): BattleTeamData | null {
  if (this.teamA && this.teamA.characterId === characterId) {
    return this.teamB;
  }
  if (this.teamB && this.teamB.characterId === characterId) {
    return this.teamA;
  }
  return null;
};

/**
 * 更新队伍比分
 * 基于BattleService: 比分更新逻辑
 */
BattleRoomSchema.methods.updateTeamScore = function(team: 'teamA' | 'teamB', score: number): void {
  if (this[team]) {
    this[team].score = score;
  }
};

/**
 * 更新队伍士气
 * 基于BattleEngine: 士气管理逻辑
 */
BattleRoomSchema.methods.updateTeamMorale = function(team: 'teamA' | 'teamB', morale: number): void {
  if (this[team]) {
    this[team].morale = morale;
  }
};

/**
 * 获取总回合数
 * 基于BattleEngine: 回合统计逻辑
 */
BattleRoomSchema.methods.getTotalRounds = function(): number {
  if (!this.result || !this.result.roundInfo) return 0;
  return this.result.roundInfo.length;
};

/**
 * 获取进球总数
 * 基于BattleEngine: 进球统计逻辑
 */
BattleRoomSchema.methods.getGoalCount = function(): number {
  return this.getTotalScore();
};

/**
 * 获取进攻次数
 * 基于BattleEngine: 进攻统计逻辑
 */
BattleRoomSchema.methods.getAttackCount = function(): number {
  if (!this.result || !this.result.roundInfo) return 0;

  // 统计所有回合中的进攻次数
  return this.result.roundInfo.filter(round => round.attackMode > 0).length;
};

/**
 * 获取队伍统计数据
 * 基于BattleEngine: 队伍统计逻辑
 */
BattleRoomSchema.methods.getTeamStats = function(team: 'teamA' | 'teamB'): any {
  const teamData = this[team];
  if (!teamData) return null;

  const isTeamA = team === 'teamA';
  const teamScore = isTeamA ? (this.result?.homeScore || 0) : (this.result?.awayScore || 0);

  // 统计该队的进攻次数
  let attackCount = 0;
  let goalCount = 0;

  if (this.result && this.result.roundInfo) {
    for (const round of this.result.roundInfo) {
      if ((isTeamA && round.attacker === 'teamA') || (!isTeamA && round.attacker === 'teamB')) {
        attackCount++;
        // 检查是否进球（比分变化）
        if ((isTeamA && round.scoreA > (round.roundIndex > 0 ? this.result.roundInfo[round.roundIndex - 1]?.scoreA || 0 : 0)) ||
            (!isTeamA && round.scoreB > (round.roundIndex > 0 ? this.result.roundInfo[round.roundIndex - 1]?.scoreB || 0 : 0))) {
          goalCount++;
        }
      }
    }
  }

  return {
    characterId: teamData.characterId,
    teamName: teamData.teamName,
    formation: teamData.formation,
    tactic: teamData.tactic,
    totalAttack: teamData.totalAttack,
    totalDefend: teamData.totalDefend,
    morale: teamData.morale,
    score: teamScore,
    heroCount: teamData.heroes.length,
    attackCount,
    goalCount,
    attackSuccessRate: attackCount > 0 ? (goalCount / attackCount * 100) : 0
  };
};

/**
 * 验证队伍数据
 * 基于BattleService: 队伍数据验证逻辑
 */
BattleRoomSchema.methods.validateTeamData = function(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // 检查teamA
  if (!this.teamA) {
    errors.push('缺少teamA数据');
  } else {
    if (!this.teamA.characterId) errors.push('teamA缺少characterId');
    if (!this.teamA.heroes || this.teamA.heroes.length === 0) errors.push('teamA缺少球员数据');
    if (this.teamA.formation <= 0) errors.push('teamA阵型无效');
  }

  // 检查teamB
  if (!this.teamB) {
    errors.push('缺少teamB数据');
  } else {
    if (!this.teamB.characterId) errors.push('teamB缺少characterId');
    if (!this.teamB.heroes || this.teamB.heroes.length === 0) errors.push('teamB缺少球员数据');
    if (this.teamB.formation <= 0) errors.push('teamB阵型无效');
  }

  return { isValid: errors.length === 0, errors };
};

/**
 * 检查是否有有效的球员数据
 * 基于BattleService: 球员数据验证逻辑
 */
BattleRoomSchema.methods.hasValidHeroes = function(): boolean {
  // 检查teamA球员
  if (!this.teamA || !this.teamA.heroes || this.teamA.heroes.length === 0) return false;

  // 检查teamB球员
  if (!this.teamB || !this.teamB.heroes || this.teamB.heroes.length === 0) return false;

  // 检查球员数据完整性
  for (const hero of this.teamA.heroes) {
    if (!hero.heroId || hero.attack < 0 || hero.defend < 0) return false;
  }

  for (const hero of this.teamB.heroes) {
    if (!hero.heroId || hero.attack < 0 || hero.defend < 0) return false;
  }

  return true;
};

/**
 * 转换为战斗回放数据
 * 基于BattleService: getBattleReplay方法逻辑
 */
BattleRoomSchema.methods.toBattleReplay = function(): any {
  return {
    roomUid: this.roomUid,
    battleType: this.battleType,
    status: this.status,
    teamAData: {
      characterId: this.teamA?.characterId,
      teamName: this.teamA?.teamName,
      formation: this.teamA?.formation,
      tactic: this.teamA?.tactic,
      heroes: this.teamA?.heroes || [],
      score: this.teamA?.score || 0
    },
    teamBData: {
      characterId: this.teamB?.characterId,
      teamName: this.teamB?.teamName,
      formation: this.teamB?.formation,
      tactic: this.teamB?.tactic,
      heroes: this.teamB?.heroes || [],
      score: this.teamB?.score || 0
    },
    battleRecord: this.result?.battleEndInfo || {},
    roundInfo: this.result?.roundInfo || [],
    winner: this.getWinner(),
    duration: this.getBattleDuration(),
    createdAt: this.createdAt,
    finishedAt: this.finishedAt
  };
};

/**
 * 转换为战斗结果数据
 * 基于BattleService: 战斗结果处理逻辑
 */
BattleRoomSchema.methods.toBattleResult = function(): BattleResult {
  return this.result || {
    roomUid: this.roomUid,
    battleType: this.battleType,
    homeScore: this.teamA?.score || 0,
    awayScore: this.teamB?.score || 0,
    winner: this.getWinner(),
    battleTime: this.finishedAt || new Date(),
    roundInfo: [],
    battleEndInfo: {}
  };
};

/**
 * 转换为客户端战斗信息
 * 基于BattleService: 客户端数据转换需求
 */
BattleRoomSchema.methods.toClientBattleInfo = function(): any {
  return {
    roomUid: this.roomUid,
    battleType: this.battleType,
    status: this.status,
    homeTeam: {
      characterId: this.teamA?.characterId,
      teamName: this.teamA?.teamName,
      score: this.teamA?.score || 0,
      formation: this.teamA?.formation,
      heroCount: this.teamA?.heroes?.length || 0
    },
    awayTeam: {
      characterId: this.teamB?.characterId,
      teamName: this.teamB?.teamName,
      score: this.teamB?.score || 0,
      formation: this.teamB?.formation,
      heroCount: this.teamB?.heroes?.length || 0
    },
    result: {
      winner: this.getWinner(),
      isDraw: this.isDraw(),
      totalScore: this.getTotalScore(),
      duration: this.getBattleDuration()
    },
    createdAt: this.createdAt,
    finishedAt: this.finishedAt
  };
};
