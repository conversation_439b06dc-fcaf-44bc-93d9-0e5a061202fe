import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { BattleService } from './battle.service';
import {
  BattleResultResponseDto,
  GetBattleReplayResponseDto
} from '../../common/dto/battle.dto';
import { Cacheable, CacheEvict } from '@libs/redis';

import { XResponse } from '@libs/common/types/result.type';

import {
  CleanExpiredRoomsPayloadDto,
  DeleteBattleRoomPayloadDto,
  GetBattleReplayPayloadDto,
  GetStatisticsPayloadDto,
  PveBattlePayloadDto,
  PvpBattlePayloadDto
} from "@match/common/dto/battle-payload.dto";

/**
 * 战斗系统控制器
 * 严格基于old项目battleService.js的接口设计
 * 
 * 核心接口：
 * - battle.pveBattle: PVE战斗计算
 * - battle.pvpBattle: PVP战斗计算
 * - battle.getBattleReplay: 获取战斗回放
 * - battle.deleteBattleRoom: 删除战斗房间
 */
@Controller()
export class BattleController {
  private readonly logger = new Logger(BattleController.name);

  constructor(private readonly battleService: BattleService) {}

  /**
   * PVE战斗
   * 基于old项目的initPveBattle接口
   */
  @MessagePattern('battle.pveBattle')
  async pveBattle(@Payload() payload: PveBattlePayloadDto): Promise<BattleResultResponseDto> {
    this.logger.log(`PVE战斗请求: ${payload.characterId}, 类型: ${payload.battleType}`);
    
    try {
      const result = await this.battleService.pveBattle(payload);
      
      if (result.code === 0) {
        this.logger.log(`PVE战斗完成: ${payload.characterId}, 比分: ${result.homeScore}:${result.awayScore}`);
      } else {
        this.logger.warn(`PVE战斗失败: ${payload.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('PVE战斗异常', error);
      return {
        code: -1,
        message: '战斗处理异常',
      };
    }
  }

  /**
   * PVP战斗
   * 基于old项目的pvpMatchBattle接口
   */
  @MessagePattern('battle.pvpBattle')
  async pvpBattle(@Payload() payload: PvpBattlePayloadDto): Promise<BattleResultResponseDto> {
    this.logger.log(`PVP战斗请求: ${payload.homeCharacterId} vs ${payload.awayCharacterId}`);
    
    try {
      const result = await this.battleService.pvpBattle(payload);
      
      if (result.code === 0) {
        this.logger.log(`PVP战斗完成: ${payload.homeCharacterId} vs ${payload.awayCharacterId}, 比分: ${result.homeScore}:${result.awayScore}`);
      } else {
        this.logger.warn(`PVP战斗失败: ${payload.homeCharacterId} vs ${payload.awayCharacterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('PVP战斗异常', error);
      return {
        code: -1,
        message: '战斗处理异常',
      };
    }
  }

  /**
   * 获取战斗回放
   * 基于old项目的战斗回放功能
   */
  @MessagePattern('battle.getBattleReplay')
  @Cacheable({
    key: 'battle:replay:#{payload.roomUid}',
    dataType: 'global',
    ttl: 3600 // 1小时缓存
  })
  async getBattleReplay(@Payload() payload: GetBattleReplayPayloadDto): Promise<GetBattleReplayResponseDto> {
    this.logger.log(`获取战斗回放: ${payload.roomUid}`);
    
    try {
      const result = await this.battleService.getBattleReplay(payload);
      
      if (result.code === 0) {
        this.logger.log(`战斗回放获取成功: ${payload.roomUid}`);
      } else {
        this.logger.warn(`战斗回放获取失败: ${payload.roomUid}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('获取战斗回放异常', error);
      return {
        code: -1,
        message: '获取战斗回放异常',
      };
    }
  }

  /**
   * 删除战斗房间
   * 基于old项目的deleteBattleRoom接口
   */
  @MessagePattern('battle.deleteBattleRoom')
  @CacheEvict({
    key: 'battle:replay:#{payload.roomUid}',
    dataType: 'global'
  })
  async deleteBattleRoom(@Payload() payload: DeleteBattleRoomPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`删除战斗房间: ${payload.roomUid}`);
    
    try {
      const result = await this.battleService.deleteBattleRoom(payload.roomUid);
      
      if (result) {
        this.logger.log(`战斗房间删除成功: ${payload.roomUid}`);
        return { code: 0, message: '删除成功' };
      } else {
        this.logger.warn(`战斗房间删除失败: ${payload.roomUid}`);
        return { code: -1, message: '删除失败' };
      }
    } catch (error) {
      this.logger.error('删除战斗房间异常', error);
      return {
        code: -1,
        message: '删除战斗房间异常',
      };
    }
  }

  /**
   * 获取战斗统计信息（管理接口）
   */
  @MessagePattern('battle.getStatistics')
  async getStatistics(@Payload() payload: GetStatisticsPayloadDto): Promise<XResponse<any>> {
    this.logger.log('获取战斗统计信息');
    
    try {
      const statistics = await this.battleService.getBattleStatistics();
      
      return {
        code: 0,
        message: '获取成功',
        data: statistics,
      };
    } catch (error) {
      this.logger.error('获取战斗统计信息异常', error);
      return {
        code: -1,
        message: '获取统计信息失败',
      };
    }
  }

  /**
   * 清理过期房间（管理接口）
   */
  @MessagePattern('battle.cleanExpiredRooms')
  async cleanExpiredRooms(@Payload() payload: CleanExpiredRoomsPayloadDto): Promise<XResponse<any>> {
    this.logger.log('清理过期战斗房间');
    
    try {
      const cleanedCount = await this.battleService.cleanExpiredRooms();
      
      return {
        code: 0,
        message: '清理完成',
        data: { cleanedCount },
      };
    } catch (error) {
      this.logger.error('清理过期房间异常', error);
      return {
        code: -1,
        message: '清理失败',
      };
    }
  }
}
