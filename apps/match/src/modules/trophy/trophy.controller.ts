import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { TrophyService } from './trophy.service';
import {
  GetTrophyCopyDataResponseDto,
  PveTrophyBattleResultResponseDto,
  BuyTrophyTimesResponseDto
} from '../../common/dto/trophy.dto';
import { Cacheable, CacheEvict } from '@libs/redis';

import { XResponse } from '@libs/common/types/result.type';

import {
  BuyTrophyTimesPayloadDto,
  GetBattleHistoryPayloadDto,
  GetStatisticsPayloadDto,
  GetTrophyCopyDataPayloadDto,
  PveTrophyBattlePayloadDto,
  ResetDailyTimesPayloadDto
} from "@match/common/dto/trophy-payload.dto";

/**
 * 杯赛系统控制器
 * 严格基于old项目trophyCopy.js的接口设计
 * 
 * 核心接口：
 * - trophy.getTrophyCopyData: 获取杯赛副本数据
 * - trophy.pveTrophyBattle: PVE杯赛战斗
 * - trophy.buyTrophyTimes: 购买杯赛次数
 */
@Controller()
export class TrophyController {
  private readonly logger = new Logger(TrophyController.name);

  constructor(private readonly trophyService: TrophyService) {}

  /**
   * 获取杯赛副本数据
   * 基于old项目的getTrophyCopyData接口
   */
  @MessagePattern('trophy.getTrophyCopyData')
  @Cacheable({
    key: 'trophy:data:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300 // 5分钟缓存
  })
  async getTrophyCopyData(@Payload() payload: GetTrophyCopyDataPayloadDto): Promise<GetTrophyCopyDataResponseDto> {
    this.logger.log(`获取杯赛副本数据: ${payload.characterId}`);
    
    try {
      const result = await this.trophyService.getTrophyCopyData(payload);
      
      if (result.code === 0) {
        this.logger.log(`杯赛数据获取成功: ${payload.characterId}, 杯赛数量: ${result.trophyCopyData?.length || 0}`);
      } else {
        this.logger.warn(`杯赛数据获取失败: ${payload.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('获取杯赛副本数据异常', error);
      return {
        code: -1,
        message: '获取杯赛副本数据异常',
      };
    }
  }

  /**
   * PVE杯赛战斗
   * 基于old项目的pveTrophyCopyBattle接口
   */
  @MessagePattern('trophy.pveTrophyBattle')
  @CacheEvict({
    key: 'trophy:data:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async pveTrophyBattle(@Payload() payload: PveTrophyBattlePayloadDto): Promise<PveTrophyBattleResultResponseDto> {
    this.logger.log(`PVE杯赛战斗: ${payload.characterId}, 杯赛${payload.trophyId}, 副本${payload.teamCopyId}`);
    
    try {
      const result = await this.trophyService.pveTrophyBattle(payload);
      
      if (result.code === 0) {
        this.logger.log(`PVE杯赛战斗成功: ${payload.characterId}, 结果: ${result.result}, 比分: ${result.selfScore}:${result.enemyScore}`);
      } else {
        this.logger.warn(`PVE杯赛战斗失败: ${payload.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('PVE杯赛战斗异常', error);
      return {
        code: -1,
        message: '战斗处理异常',
      };
    }
  }

  /**
   * 购买杯赛次数
   * 基于old项目的buyTrophyTimes接口
   */
  @MessagePattern('trophy.buyTrophyTimes')
  @CacheEvict({
    key: 'trophy:data:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async buyTrophyTimes(@Payload() payload: BuyTrophyTimesPayloadDto): Promise<BuyTrophyTimesResponseDto> {
    this.logger.log(`购买杯赛次数: ${payload.characterId}, 杯赛${payload.trophyId}, 次数${payload.num}`);
    
    try {
      const result = await this.trophyService.buyTrophyTimes(payload);
      
      if (result.code === 0) {
        this.logger.log(`购买杯赛次数成功: ${payload.characterId}, 增加: ${result.addedTimes}次, 费用: ${result.totalCost}`);
      } else {
        this.logger.warn(`购买杯赛次数失败: ${payload.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('购买杯赛次数异常', error);
      return {
        code: -1,
        message: '购买失败',
      };
    }
  }

  /**
   * 获取杯赛统计信息（管理接口）
   */
  @MessagePattern('trophy.getStatistics')
  async getStatistics(@Payload() payload: GetStatisticsPayloadDto): Promise<XResponse<any>> {
    this.logger.log('获取杯赛统计信息');
    
    try {
      // 这里可以添加统计信息的获取逻辑
      return {
        code: 0,
        message: '获取成功',
        data: {
          totalCharacters: 0,
          activeToday: 0,
          totalBattles: 0,
          timestamp: new Date(),
        },
      };
    } catch (error) {
      this.logger.error('获取杯赛统计信息异常', error);
      return {
        code: -1,
        message: '获取统计信息失败',
      };
    }
  }

  /**
   * 重置每日杯赛次数（管理接口）
   * 修复：添加真正的重置逻辑和正确的返回格式
   */
  @MessagePattern('trophy.resetDailyTimes')
  async resetDailyTimes(@Payload() payload: ResetDailyTimesPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`重置每日杯赛次数: ${payload.characterId || 'all'}`);

    try {
      // 调用服务层的重置逻辑
      const result = await this.trophyService.resetDailyTimes(payload.characterId);

      if (result.success) {
        this.logger.log(`重置每日杯赛次数成功: ${result.message}`);
        return {
          code: 0,
          message: '重置成功',
          data: {
            message: result.message,
            resetCount: result.resetCount || 0
          }
        };
      } else {
        this.logger.error(`重置每日杯赛次数失败: ${result.message}`);
        return {
          code: -1,
          message: result.message || '重置失败',
        };
      }
    } catch (error) {
      this.logger.error('重置每日杯赛次数异常', error);
      return {
        code: -1,
        message: '重置失败',
      };
    }
  }

  /**
   * 获取杯赛战斗历史（管理接口）
   */
  @MessagePattern('trophy.getBattleHistory')
  async getBattleHistory(@Payload() payload: GetBattleHistoryPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取杯赛战斗历史: ${payload.characterId}`);
    
    try {
      // 这里可以添加获取战斗历史的逻辑
      return {
        code: 0,
        message: '获取成功',
        data: [],
      };
    } catch (error) {
      this.logger.error('获取杯赛战斗历史异常', error);
      return {
        code: -1,
        message: '获取失败',
      };
    }
  }
}
