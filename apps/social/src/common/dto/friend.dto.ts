/**
 * 好友系统相关的数据传输对象
 */

import { IsString, IsNumber, IsOptional, IsBoolean, IsArray, IsEnum, Min, Max, Length, ValidateNested } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { FriendStatus } from '../schemas/friend.schema';

// 添加好友DTO
export class AddFriendDto {
  @ApiProperty({ description: '目标角色ID' })
  @IsString()
  targetCharacterId: string;

  @ApiPropertyOptional({ description: '申请消息' })
  @IsOptional()
  @IsString()
  @Length(0, 100)
  message?: string;

  @ApiPropertyOptional({ description: '经度' })
  @IsOptional()
  @IsNumber()
  longitude?: number;

  @ApiPropertyOptional({ description: '纬度' })
  @IsOptional()
  @IsNumber()
  latitude?: number;
}

// 处理好友申请DTO
export class HandleFriendApplyDto {
  @ApiProperty({ description: '申请者角色ID' })
  @IsString()
  applicantCharacterId: string;

  @ApiProperty({ description: '是否接受申请' })
  @IsBoolean()
  accept: boolean;
}

// 删除好友DTO
export class RemoveFriendDto {
  @ApiProperty({ description: '好友角色ID' })
  @IsString()
  friendCharacterId: string;
}

// 更新位置DTO
export class UpdateLocationDto {
  @ApiProperty({ description: '经度' })
  @IsNumber()
  longitude: number;

  @ApiProperty({ description: '纬度' })
  @IsNumber()
  latitude: number;
}

// 屏蔽玩家DTO
export class BlockCharacterDto {
  @ApiProperty({ description: '目标玩家ID' })
  @IsString()
  targetCharacterId: string;
}

// 搜索好友DTO
export class SearchFriendDto {
  @ApiPropertyOptional({ description: '搜索关键词（玩家名称或ID）' })
  @IsOptional()
  @IsString()
  @Length(1, 50)
  keyword?: string;

  @ApiPropertyOptional({ description: '等级范围（最小）' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  minLevel?: number;

  @ApiPropertyOptional({ description: '等级范围（最大）' })
  @IsOptional()
  @IsNumber()
  @Max(100)
  maxLevel?: number;

  @ApiPropertyOptional({ description: '是否只显示在线好友' })
  @IsOptional()
  @IsBoolean()
  onlineOnly?: boolean;

  @ApiPropertyOptional({ description: '距离范围（米）' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxDistance?: number;

  @ApiPropertyOptional({ description: '当前经度' })
  @IsOptional()
  @IsNumber()
  longitude?: number;

  @ApiPropertyOptional({ description: '当前纬度' })
  @IsOptional()
  @IsNumber()
  latitude?: number;
}

// 好友信息响应DTO
export class FriendInfoDto {
  @ApiProperty({ description: '玩家ID' })
  characterId: string;

  @ApiProperty({ description: '玩家名称' })
  name: string;

  @ApiProperty({ description: '等级' })
  level: number;

  @ApiProperty({ description: '段位' })
  honor: number;

  @ApiProperty({ description: '头像' })
  faceIcon: string;

  @ApiProperty({ description: '头像URL' })
  faceUrl: string;

  @ApiProperty({ description: 'VIP等级' })
  vip: number;

  @ApiProperty({ description: '奖杯数' })
  trophy: number;

  @ApiProperty({ description: '添加时间' })
  addTime: number;

  @ApiProperty({ description: '最后在线时间' })
  lastOnlineTime: number;

  @ApiProperty({ description: '是否在线' })
  isOnline: boolean;

  @ApiProperty({ description: '区服ID' })
  serverId: string;

  @ApiPropertyOptional({ description: '距离（米）' })
  distance?: number;
}

// 好友申请信息DTO
export class FriendApplyDto {
  @ApiProperty({ description: '申请者玩家ID' })
  characterId: string;

  @ApiProperty({ description: '申请者名称' })
  name: string;

  @ApiProperty({ description: '等级' })
  level: number;

  @ApiProperty({ description: '段位' })
  honor: number;

  @ApiProperty({ description: '头像' })
  faceIcon: string;

  @ApiProperty({ description: '头像URL' })
  faceUrl: string;

  @ApiProperty({ description: 'VIP等级' })
  vip: number;

  @ApiProperty({ description: '奖杯数' })
  trophy: number;

  @ApiProperty({ description: '申请时间' })
  applyTime: number;

  @ApiProperty({ description: '申请消息' })
  message: string;

  @ApiProperty({ description: '区服ID' })
  serverId: string;
}

// 好友列表响应DTO
export class FriendListDto {
  @ApiProperty({ description: '好友列表' })
  friends: FriendInfoDto[];

  @ApiProperty({ description: '总好友数' })
  totalFriends: number;

  @ApiProperty({ description: '在线好友数' })
  onlineFriends: number;

  @ApiProperty({ description: '待处理申请数' })
  pendingApplies: number;

  @ApiProperty({ description: '是否可以添加更多好友' })
  canAddMoreFriends: boolean;
}

// 好友申请列表DTO
export class FriendApplyListDto {
  @ApiProperty({ description: '收到的申请列表' })
  receivedApplies: FriendApplyDto[];

  @ApiProperty({ description: '发出的申请列表' })
  sentApplies: FriendApplyDto[];

  @ApiProperty({ description: '待处理申请数' })
  pendingCount: number;
}

// 附近玩家DTO
export class NearbyCharactersDto {
  @ApiProperty({ description: '当前经度' })
  @IsNumber()
  longitude: number;

  @ApiProperty({ description: '当前纬度' })
  @IsNumber()
  latitude: number;

  @ApiPropertyOptional({ description: '搜索半径（米）', minimum: 100, maximum: 50000 })
  @IsOptional()
  @IsNumber()
  @Min(100)
  @Max(50000)
  radius?: number;

  @ApiPropertyOptional({ description: '最大返回数量', minimum: 1, maximum: 50 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number;
}



// 好友统计DTO
export class FriendStatsDto {
  @ApiProperty({ description: '总好友数' })
  totalFriends: number;

  @ApiProperty({ description: '在线好友数' })
  onlineFriends: number;

  @ApiProperty({ description: '今日新增好友数' })
  todayNewFriends: number;

  @ApiProperty({ description: '本周新增好友数' })
  weekNewFriends: number;

  @ApiProperty({ description: '待处理申请数' })
  pendingApplies: number;

  @ApiProperty({ description: '黑名单数量' })
  blockedCharacters: number;

  @ApiProperty({ description: '好友等级分布' })
  levelDistribution: Record<string, number>;

  @ApiProperty({ description: '好友VIP分布' })
  vipDistribution: Record<string, number>;
}
