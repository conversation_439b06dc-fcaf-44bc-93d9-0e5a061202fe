/**
 * 荣誉墙服务 - Result模式完整适配版本
 *
 * 核心功能：
 * - 荣誉等级管理和经验系统
 * - 荣誉任务进度跟踪和奖励发放
 * - 特殊成就解锁和记录
 * - 荣誉历史记录管理
 *
 * Result模式适配特性：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public和private方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装所有关键业务操作
 * - 标准化的错误处理和业务逻辑验证
 * - 完整的中文注释和操作日志
 *
 * 基于old项目honorWall.js业务逻辑迁移和优化
 */

import { Injectable } from '@nestjs/common';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { HonorRepository } from '@activity/common/repositories/honor.repository';
import { HonorTaskStatus } from '@activity/common/schemas/honor.schema';
import { RepositoryResultWrapper, XResult, XResultUtils } from '@libs/common/types/result.type';
import { BaseService } from '@libs/common/service/base-service';
import { MicroserviceClientService } from '@libs/service-mesh';

@Injectable()
export class HonorService extends BaseService {
  constructor(
    private readonly honorRepository: HonorRepository,
    microserviceClient: MicroserviceClientService,
  ) {
    super('HonorService', microserviceClient);
  }

  /**
   * 获取荣誉墙信息
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑，完善数据验证
   */
  async getHonorInfo(uid: string, serverId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!uid || !serverId) {
        return XResultUtils.error('参数不完整：uid和serverId不能为空', 'INVALID_PARAMETERS');
      }

      const honorResult = await this.honorRepository.getOrCreateHonor(uid, serverId);
      if (XResultUtils.isFailure(honorResult)) {
        return XResultUtils.error(`获取荣誉墙信息失败: ${honorResult.message}`, honorResult.code);
      }

      const honor = honorResult.data;
      if (!honor) {
        return XResultUtils.error('荣誉墙数据不存在', 'HONOR_DATA_NOT_FOUND');
      }

      // 计算可领取奖励数量
      const availableRewards = honor.honorTasks.filter(task => task.status === HonorTaskStatus.COMPLETED);

      // 计算下一等级所需经验
      const nextLevelExpResult = await this.calculateNextLevelExperience(honor.currentLevel);
      if (XResultUtils.isFailure(nextLevelExpResult)) {
        this.logger.warn(`计算下一等级经验失败: ${nextLevelExpResult.message}`);
      }

      const responseData = {
        uid,
        currentLevel: honor.currentLevel,
        totalExperience: honor.totalExperience,
        honorTasks: honor.honorTasks,
        totalTasksCompleted: honor.totalTasksCompleted,
        totalRewardsClaimed: honor.totalRewardsClaimed,
        specialAchievements: honor.specialAchievements,
        levelHistory: honor.levelHistory,
        availableRewards: availableRewards,
        availableRewardsCount: availableRewards.length,
        lastUpdateTime: honor.lastUpdateTime || Date.now(),
      };

      this.logger.log(`获取荣誉墙信息成功: ${uid}, 等级: ${honor.currentLevel}, 可领取奖励: ${availableRewards.length}个`);

      return XResultUtils.ok(responseData);
    }, {
      reason: 'get_honor_info',
      metadata: {uid, serverId}
    });
  }

  /**
   * 领取荣誉任务奖励
   * 已适配Result模式：完善业务逻辑验证，增强错误处理，添加奖励发放逻辑
   */
  async getHonorTaskReward(uid: string, serverId: string, taskId: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!uid || !serverId || taskId === undefined || taskId === null) {
        return XResultUtils.error('参数不完整：uid、serverId和taskId不能为空', 'INVALID_PARAMETERS');
      }

      if (taskId < 0) {
        return XResultUtils.error('任务ID无效', 'INVALID_TASK_ID');
      }

      const honorResult = await this.honorRepository.getOrCreateHonor(uid, serverId);
      if (XResultUtils.isFailure(honorResult)) {
        return XResultUtils.error(`获取荣誉墙信息失败: ${honorResult.message}`, honorResult.code);
      }

      const honor = honorResult.data;
      if (!honor) {
        return XResultUtils.error('荣誉墙数据不存在', 'HONOR_DATA_NOT_FOUND');
      }

      // 查找任务
      const task = honor.getHonorTask(taskId);
      if (!task) {
        return XResultUtils.error(ErrorMessages[ErrorCode.HONOR_TASK_NOT_FOUND], ErrorCode.HONOR_TASK_NOT_FOUND);
      }

      // 检查任务状态
      if (task.status === HonorTaskStatus.CLAIMED) {
        return XResultUtils.failure('任务奖励已领取', 'HONOR_REWARD_ALREADY_CLAIMED', {
          taskId,
          claimTime: task.claimTime,
          status: task.status
        });
      }

      if (!honor.canClaimReward(taskId)) {
        return XResultUtils.failure(ErrorMessages[ErrorCode.HONOR_TASK_NOT_COMPLETED], ErrorCode.HONOR_TASK_NOT_COMPLETED, {
          taskId,
          currentProgress: task.currentProgress,
          targetProgress: task.targetProgress,
          status: task.status
        });
      }

      // 获取任务奖励经验
      const rewardExpResult = await this.getTaskRewardExperience(taskId);
      if (XResultUtils.isFailure(rewardExpResult)) {
        return XResultUtils.error(`获取任务奖励经验失败: ${rewardExpResult.message}`, rewardExpResult.code);
      }

      const rewardExp = rewardExpResult.data;
      const oldLevel = honor.currentLevel;

      // 领取奖励
      const success = honor.claimTaskReward(taskId);
      if (!success) {
        return XResultUtils.error(ErrorMessages[ErrorCode.HONOR_REWARD_CLAIM_FAILED], ErrorCode.HONOR_REWARD_CLAIM_FAILED);
      }

      // 添加经验
      const leveledUp = honor.addExperience(rewardExp);

      // 保存数据
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await honor.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存荣誉墙信息失败: ${saveResult.message}`, saveResult.code);
      }

      // 发放奖励到玩家背包（调用微服务）
      const rewardDistributionResult = await this.distributeTaskRewards(uid, serverId, task.rewards);
      if (XResultUtils.isFailure(rewardDistributionResult)) {
        this.logger.warn(`发放任务奖励失败: ${uid}, 任务: ${taskId}, ${rewardDistributionResult.message}`);
        // 奖励发放失败不影响任务完成状态，但需要记录日志
      }

      this.logger.log(`荣誉任务奖励领取成功: ${uid}, 任务: ${taskId}, 经验: ${rewardExp}, 升级: ${leveledUp}`);

      const responseData = {
        success: true,
        message: '荣誉任务奖励领取成功',
        taskId,
        rewards: task.rewards,
        experienceGained: rewardExp,
        leveledUp,
        oldLevel,
        newLevel: honor.currentLevel,
        totalExperience: honor.totalExperience,
        rewardDistributed: XResultUtils.isSuccess(rewardDistributionResult),
        operationTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'get_honor_task_reward',
      metadata: {uid, serverId, taskId}
    });
  }

  /**
   * 更新荣誉任务进度
   * 已适配Result模式：完善参数验证，增强业务逻辑处理，优化返回数据结构
   */
  async updateHonorTaskProgress(uid: string, serverId: string, taskId: number, progress: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!uid || !serverId || taskId === undefined || taskId === null || progress === undefined || progress === null) {
        return XResultUtils.error('参数不完整：uid、serverId、taskId和progress不能为空', 'INVALID_PARAMETERS');
      }

      if (taskId < 0) {
        return XResultUtils.error('任务ID无效', 'INVALID_TASK_ID');
      }

      if (progress < 0) {
        return XResultUtils.error('进度值不能为负数', 'INVALID_PROGRESS_VALUE');
      }

      const honorResult = await this.honorRepository.getOrCreateHonor(uid, serverId);
      if (XResultUtils.isFailure(honorResult)) {
        return XResultUtils.error(`获取荣誉墙信息失败: ${honorResult.message}`, honorResult.code);
      }

      const honor = honorResult.data;
      if (!honor) {
        return XResultUtils.error('荣誉墙数据不存在', 'HONOR_DATA_NOT_FOUND');
      }

      // 检查任务是否存在
      const task = honor.getHonorTask(taskId);
      if (!task) {
        return XResultUtils.error(ErrorMessages[ErrorCode.HONOR_TASK_NOT_FOUND], ErrorCode.HONOR_TASK_NOT_FOUND);
      }

      // 检查任务状态
      if (task.status === HonorTaskStatus.CLAIMED) {
        return XResultUtils.failure('任务已完成并领取奖励，无法更新进度', 'HONOR_TASK_ALREADY_CLAIMED', {
          taskId,
          status: task.status,
          claimTime: task.claimTime
        });
      }

      const oldProgress = task.currentProgress;
      const oldStatus = task.status;

      // 更新任务进度
      const success = honor.updateTaskProgress(taskId, progress);
      if (!success) {
        this.logger.warn(`荣誉任务进度更新失败: ${uid}, 任务: ${taskId}, 进度: ${progress}`);
        return XResultUtils.failure('荣誉任务进度更新失败', 'HONOR_PROGRESS_UPDATE_FAILED', {
          taskId,
          requestedProgress: progress,
          currentProgress: oldProgress
        });
      }

      // 保存数据
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await honor.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存荣誉墙信息失败: ${saveResult.message}`, saveResult.code);
      }

      // 获取更新后的任务状态
      const updatedTask = honor.getHonorTask(taskId);
      const isCompleted = updatedTask && updatedTask.status === HonorTaskStatus.COMPLETED;
      const statusChanged = oldStatus !== updatedTask.status;

      this.logger.log(`荣誉任务进度更新成功: ${uid}, 任务: ${taskId}, 进度: ${oldProgress} -> ${updatedTask.currentProgress}, 完成: ${isCompleted}`);

      const responseData = {
        success: true,
        message: isCompleted ? '任务已完成，可领取奖励' : '任务进度更新成功',
        taskId,
        oldProgress,
        currentProgress: updatedTask.currentProgress,
        targetProgress: updatedTask.targetProgress,
        progressPercentage: Math.min(100, Math.round((updatedTask.currentProgress / updatedTask.targetProgress) * 100)),
        isCompleted,
        statusChanged,
        oldStatus,
        newStatus: updatedTask.status,
        canClaimReward: isCompleted,
        operationTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'update_honor_task_progress',
      metadata: {uid, serverId, taskId, progress}
    });
  }

  /**
   * 添加荣誉任务
   * 已适配Result模式：完善任务配置验证，增强业务逻辑处理，优化返回数据结构
   */
  async addHonorTask(uid: string, serverId: string, taskConfig: any): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!uid || !serverId || !taskConfig) {
        return XResultUtils.error('参数不完整：uid、serverId和taskConfig不能为空', 'INVALID_PARAMETERS');
      }

      // 任务配置验证
      const configValidationResult = await this.validateTaskConfig(taskConfig);
      if (XResultUtils.isFailure(configValidationResult)) {
        return XResultUtils.error(`任务配置验证失败: ${configValidationResult.message}`, configValidationResult.code);
      }

      const honorResult = await this.honorRepository.getOrCreateHonor(uid, serverId);
      if (XResultUtils.isFailure(honorResult)) {
        return XResultUtils.error(`获取荣誉墙信息失败: ${honorResult.message}`, honorResult.code);
      }

      const honor = honorResult.data;
      if (!honor) {
        return XResultUtils.error('荣誉墙数据不存在', 'HONOR_DATA_NOT_FOUND');
      }

      // 检查任务是否已存在
      const existingTask = honor.getHonorTask(taskConfig.taskId);
      if (existingTask) {
        return XResultUtils.failure('荣誉任务已存在', ErrorCode.HONOR_TASK_ALREADY_EXISTS, {
          taskId: taskConfig.taskId,
          existingTaskStatus: existingTask.status,
          existingTaskProgress: existingTask.currentProgress
        });
      }

      // 检查任务数量限制
      const maxTasksResult = await this.getMaxTasksLimit();
      if (XResultUtils.isSuccess(maxTasksResult) && honor.honorTasks.length >= maxTasksResult.data) {
        return XResultUtils.failure('荣誉任务数量已达上限', 'HONOR_TASKS_LIMIT_REACHED', {
          currentTaskCount: honor.honorTasks.length,
          maxTasksLimit: maxTasksResult.data
        });
      }

      // 创建新任务（只使用schema中存在的字段）
      const newTask = {
        taskId: taskConfig.taskId,
        taskType: taskConfig.taskType,
        currentProgress: 0,
        targetProgress: taskConfig.targetProgress,
        status: HonorTaskStatus.IN_PROGRESS,
        startTime: Date.now(),
        completeTime: 0,
        claimTime: 0,
        rewards: taskConfig.rewards || {},
      };

      // 添加任务到荣誉墙
      honor.honorTasks.push(newTask);

      // 保存数据
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await honor.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存荣誉墙信息失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`荣誉任务添加成功: ${uid}, 任务: ${taskConfig.taskId}, 类型: ${taskConfig.taskType}, 目标进度: ${taskConfig.targetProgress}`);

      const responseData = {
        success: true,
        message: '荣誉任务添加成功',
        task: newTask,
        totalTasks: honor.honorTasks.length,
        operationTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'add_honor_task',
      metadata: {uid, serverId, taskId: taskConfig?.taskId, taskType: taskConfig?.taskType}
    });
  }

  /**
   * 获取荣誉等级奖励
   * 已适配Result模式：完善等级验证，实现奖励发放逻辑，增强业务处理
   */
  async getHonorLevelReward(uid: string, serverId: string, level: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!uid || !serverId || level === undefined || level === null) {
        return XResultUtils.error('参数不完整：uid、serverId和level不能为空', 'INVALID_PARAMETERS');
      }

      if (level < 1) {
        return XResultUtils.error('等级必须大于0', 'INVALID_LEVEL');
      }

      const honorResult = await this.honorRepository.getOrCreateHonor(uid, serverId);
      if (XResultUtils.isFailure(honorResult)) {
        return XResultUtils.error(`获取荣誉墙信息失败: ${honorResult.message}`, honorResult.code);
      }

      const honor = honorResult.data;
      if (!honor) {
        return XResultUtils.error('荣誉墙数据不存在', 'HONOR_DATA_NOT_FOUND');
      }

      // 检查等级是否达到
      if (level > honor.currentLevel) {
        return XResultUtils.failure('荣誉等级未达到', ErrorCode.HONOR_LEVEL_NOT_REACHED, {
          requestedLevel: level,
          currentLevel: honor.currentLevel,
          requiredExperience: await this.calculateLevelRequiredExperience(level)
        });
      }

      // 查找等级记录
      const levelRecord = honor.levelHistory.find(record => record.level === level);
      if (!levelRecord) {
        return XResultUtils.error('荣誉等级记录不存在', ErrorCode.HONOR_LEVEL_RECORD_NOT_FOUND);
      }

      // 检查奖励是否已领取（通过levelRewards字段判断）
      if (levelRecord.levelRewards && Object.keys(levelRecord.levelRewards).length > 0) {
        return XResultUtils.failure('等级奖励已领取', 'HONOR_LEVEL_REWARD_ALREADY_CLAIMED', {
          level,
          achieveTime: levelRecord.achieveTime,
          rewards: levelRecord.levelRewards
        });
      }

      // 获取等级奖励配置
      const levelRewardsResult = await this.getLevelRewardsConfig(level);
      if (XResultUtils.isFailure(levelRewardsResult)) {
        return XResultUtils.error(`获取等级奖励配置失败: ${levelRewardsResult.message}`, levelRewardsResult.code);
      }

      const levelRewards = levelRewardsResult.data;

      // 标记奖励已领取（更新levelRewards字段）
      levelRecord.levelRewards = levelRewards;

      // 发放等级奖励到玩家背包
      const rewardDistributionResult = await this.distributeLevelRewards(uid, serverId, levelRewards);
      if (XResultUtils.isFailure(rewardDistributionResult)) {
        this.logger.warn(`发放等级奖励失败: ${uid}, 等级: ${level}, ${rewardDistributionResult.message}`);
        // 奖励发放失败不影响领取状态，但需要记录日志
      }

      // 保存数据
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await honor.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存荣誉墙信息失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`荣誉等级奖励领取成功: ${uid}, 等级: ${level}, 奖励数量: ${levelRewards.length}`);

      const responseData = {
        success: true,
        message: '荣誉等级奖励领取成功',
        level,
        rewards: levelRewards,
        rewardDistributed: XResultUtils.isSuccess(rewardDistributionResult),
        currentLevel: honor.currentLevel,
        totalExperience: honor.totalExperience,
        operationTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'get_honor_level_reward',
      metadata: {uid, serverId, level}
    });
  }

  /**
   * 触发荣誉任务检查
   * 已适配Result模式：完善触发逻辑，增强进度计算，优化批量更新处理
   */
  async triggerHonorTask(uid: string, serverId: string, triggerType: number, param?: any): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!uid || !serverId || triggerType === undefined || triggerType === null) {
        return XResultUtils.error('参数不完整：uid、serverId和triggerType不能为空', 'INVALID_PARAMETERS');
      }

      if (triggerType < 0) {
        return XResultUtils.error('触发类型无效', 'INVALID_TRIGGER_TYPE');
      }

      const honorResult = await this.honorRepository.getOrCreateHonor(uid, serverId);
      if (XResultUtils.isFailure(honorResult)) {
        return XResultUtils.error(`获取荣誉墙信息失败: ${honorResult.message}`, honorResult.code);
      }

      const honor = honorResult.data;
      if (!honor) {
        return XResultUtils.error('荣誉墙数据不存在', 'HONOR_DATA_NOT_FOUND');
      }

      let updatedTasks = 0;
      let completedTasks = 0;
      const updatedTaskDetails = [];

      // 遍历所有进行中的任务
      for (const task of honor.honorTasks) {
        if (task.status !== HonorTaskStatus.IN_PROGRESS) continue;

        // 检查任务类型是否匹配
        if (task.taskType === triggerType) {
          const oldProgress = task.currentProgress;
          const oldStatus = task.status;

          // 计算进度增量
          const progressIncrementResult = await this.calculateProgressIncrement(triggerType, param);
          if (XResultUtils.isFailure(progressIncrementResult)) {
            this.logger.warn(`计算进度增量失败: ${uid}, 任务: ${task.taskId}, ${progressIncrementResult.message}`);
            continue;
          }

          const progressIncrement = progressIncrementResult.data;
          const newProgress = task.currentProgress + progressIncrement;

          // 更新任务进度
          if (honor.updateTaskProgress(task.taskId, newProgress)) {
            updatedTasks++;

            const updatedTask = honor.getHonorTask(task.taskId);
            const isNewlyCompleted = oldStatus === HonorTaskStatus.IN_PROGRESS &&
              updatedTask.status === HonorTaskStatus.COMPLETED;

            if (isNewlyCompleted) {
              completedTasks++;
            }

            updatedTaskDetails.push({
              taskId: task.taskId,
              taskType: task.taskType,
              oldProgress,
              newProgress: updatedTask.currentProgress,
              targetProgress: updatedTask.targetProgress,
              progressIncrement,
              oldStatus,
              newStatus: updatedTask.status,
              isNewlyCompleted,
            });
          }
        }
      }

      // 如果有任务更新，保存数据
      if (updatedTasks > 0) {
        const saveResult = await RepositoryResultWrapper.wrap(async () => {
          return await honor.save();
        });
        if (XResultUtils.isFailure(saveResult)) {
          return XResultUtils.error(`保存荣誉墙信息失败: ${saveResult.message}`, saveResult.code);
        }

        this.logger.log(`荣誉任务触发成功: ${uid}, 类型: ${triggerType}, 更新任务数: ${updatedTasks}, 新完成任务数: ${completedTasks}`);
      }

      const responseData = {
        success: true,
        message: updatedTasks > 0 ? '荣誉任务进度更新成功' : '没有匹配的任务需要更新',
        triggerType,
        updatedTasksCount: updatedTasks,
        completedTasksCount: completedTasks,
        updatedTaskDetails,
        param,
        operationTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'trigger_honor_task',
      metadata: {uid, serverId, triggerType, param}
    });
  }

  // ==================== 私有方法 - 已适配Result模式 ====================

  /**
   * 获取任务奖励经验
   * 已适配Result模式：返回XResult类型，保持原有逻辑不变
   */
  private async getTaskRewardExperience(taskId: number): Promise<XResult<number>> {
    // TODO: 从配置表获取
    const defaultExperience = 100; // 默认100经验
    return XResultUtils.ok(defaultExperience);
  }

  /**
   * 计算进度增量
   * 已适配Result模式：返回XResult类型，保持原有计算逻辑
   */
  private async calculateProgressIncrement(triggerType: number, param?: any): Promise<XResult<number>> {
    // TODO: 根据触发类型和参数计算进度增量
    let increment: number;

    switch (triggerType) {
      case 1: // 完成比赛
        increment = 1;
        break;
      case 2: // 获得胜利
        increment = 1;
        break;
      case 3: // 签约球员
        increment = 1;
        break;
      case 4: // 升级球员
        increment = param?.levels || 1;
        break;
      case 5: // 获得金币
        increment = param?.amount || 1;
        break;
      default:
        increment = 1;
        break;
    }

    return XResultUtils.ok(increment);
  }

  /**
   * 发放任务奖励到玩家背包
   * 已适配Result模式：返回XResult类型，调用微服务发放奖励
   */
  private async distributeTaskRewards(uid: string, serverId: string, rewards: any[]): Promise<XResult<void>> {
    // TODO: 调用背包服务发放奖励
    // const result = await this.callMicroservice(
    //   MICROSERVICE_NAMES.INVENTORY_SERVICE,
    //   'inventory.addItems',
    //   { uid, serverId, items: rewards }
    // );
    //
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`发放任务奖励失败: ${result.message}`, result.code);
    // }

    // 暂时返回成功，等待实际微服务调用实现
    return XResultUtils.ok(undefined);
  }

  /**
   * 发放等级奖励到玩家背包
   * 已适配Result模式：返回XResult类型，调用微服务发放等级奖励
   */
  private async distributeLevelRewards(uid: string, serverId: string, rewards: any[]): Promise<XResult<void>> {
    // TODO: 调用背包服务发放等级奖励
    // const result = await this.callMicroservice(
    //   MICROSERVICE_NAMES.INVENTORY_SERVICE,
    //   'inventory.addItems',
    //   { uid, serverId, items: rewards }
    // );
    //
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`发放等级奖励失败: ${result.message}`, result.code);
    // }

    // 暂时返回成功，等待实际微服务调用实现
    return XResultUtils.ok(undefined);
  }

  /**
   * 验证任务配置
   * 已适配Result模式：返回XResult类型，验证任务配置的基本字段
   */
  private async validateTaskConfig(taskConfig: any): Promise<XResult<void>> {
    if (!taskConfig) {
      return XResultUtils.error('任务配置不能为空', 'TASK_CONFIG_EMPTY');
    }

    if (!taskConfig.taskId || typeof taskConfig.taskId !== 'number') {
      return XResultUtils.error('任务ID无效', 'INVALID_TASK_ID');
    }

    if (!taskConfig.taskType || typeof taskConfig.taskType !== 'number') {
      return XResultUtils.error('任务类型无效', 'INVALID_TASK_TYPE');
    }

    if (!taskConfig.targetProgress || typeof taskConfig.targetProgress !== 'number' || taskConfig.targetProgress <= 0) {
      return XResultUtils.error('目标进度无效', 'INVALID_TARGET_PROGRESS');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 获取最大任务数量限制
   * 已适配Result模式：返回XResult类型，获取任务数量限制
   */
  private async getMaxTasksLimit(): Promise<XResult<number>> {
    // TODO: 从配置表获取最大任务数量限制
    const defaultMaxTasks = 50;
    return XResultUtils.ok(defaultMaxTasks);
  }

  /**
   * 计算下一等级所需经验
   * 已适配Result模式：返回XResult类型，计算下一等级经验需求
   */
  private async calculateNextLevelExperience(currentLevel: number): Promise<XResult<number>> {
    // TODO: 从配置表获取等级经验需求
    const baseExp = 1000;
    const nextLevelExp = baseExp * (currentLevel + 1);
    return XResultUtils.ok(nextLevelExp);
  }

  /**
   * 获取等级奖励配置
   * 已适配Result模式：返回XResult类型，获取指定等级的奖励配置
   */
  private async getLevelRewardsConfig(level: number): Promise<XResult<any[]>> {
    // TODO: 从配置表获取等级奖励
    const defaultRewards = [
      {type: 'gold', amount: level * 1000},
      {type: 'exp', amount: level * 100}
    ];
    return XResultUtils.ok(defaultRewards);
  }

  /**
   * 计算等级所需经验
   * 已适配Result模式：返回XResult类型，计算指定等级的总经验需求
   */
  private async calculateLevelRequiredExperience(level: number): Promise<XResult<number>> {
    if (level < 1) {
      return XResultUtils.error('等级必须大于0', 'INVALID_LEVEL');
    }

    // TODO: 从配置表获取等级经验公式
    const baseExp = 1000;
    const requiredExp = baseExp * level;
    return XResultUtils.ok(requiredExp);
  }
}