/**
 * 任务系统服务 - Result模式完整适配版本
 *
 * 核心功能：
 * - 任务进度管理和状态跟踪
 * - 任务奖励发放和完成记录
 * - 每日/每周任务刷新机制
 * - 任务统计和数据分析
 *
 * Result模式适配特性：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public和private方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装复杂业务操作
 * - 标准化的错误处理和业务逻辑验证
 * - 完整的中文注释和操作日志
 *
 * 基于old项目任务系统业务逻辑迁移和优化
 */

import { Injectable } from '@nestjs/common';
import { TaskDocument, TaskType, TaskStatus, TaskProgress } from '@activity/common/schemas/task.schema';
import { TaskRepository } from '@activity/common/repositories/task.repository';
import {
  UpdateTaskProgressDto,
  ClaimTaskRewardDto,
  GetTaskListDto,
  RefreshTaskDto,
  TaskListDto,
  TaskStatsDto,
  TaskStatsResponseDto,
  BatchUpdateTaskProgressDto,
  AddTaskDto
} from '@activity/common/dto/task.dto';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { RepositoryResultWrapper, XResult, XResultUtils } from '@libs/common/types/result.type';
import { BaseService } from '@libs/common/service/base-service';
import { MicroserviceClientService } from '@libs/service-mesh';

@Injectable()
export class TaskService extends BaseService {
  constructor(
    private readonly taskRepository: TaskRepository,
    microserviceClient: MicroserviceClientService,
  ) {
    super('TaskService', microserviceClient);
  }

  /**
   * 获取任务列表
   * 已适配Result模式：简单的业务操作，不使用executeBusinessOperation
   */
  async getTaskList(characterId: string, serverId: string, query?: GetTaskListDto): Promise<XResult<TaskListDto>> {
    // 参数验证
    if (!characterId || !serverId) {
      return XResultUtils.error('参数不完整：characterId和serverId不能为空', 'INVALID_PARAMETERS');
    }

    // 获取任务数据
    const taskResult = await this.taskRepository.getOrCreateTask(characterId, serverId);
    if (XResultUtils.isFailure(taskResult)) {
      return XResultUtils.error(`获取任务数据失败: ${taskResult.message}`, taskResult.code);
    }

    const task = taskResult.data;
    if (!task) {
      return XResultUtils.error('任务数据不存在', 'TASK_DATA_NOT_FOUND');
    }

    // 过滤任务
    const filterTasks = (tasks: TaskProgress[]) => {
      let filtered = tasks;

      if (query?.status !== undefined) {
        filtered = filtered.filter(t => t.status === query.status);
      }

      if (query?.claimableOnly) {
        filtered = filtered.filter(t => t.status === TaskStatus.COMPLETED);
      }

      if (!query?.includeExpired) {
        filtered = filtered.filter(t => !t.isExpired);
      }

      return filtered.map(t => this.toTaskProgressDto(t));
    };

    const responseData: TaskListDto = {
      dailyTasks: query?.taskType === undefined || query.taskType === TaskType.DAILY
        ? filterTasks(task.dailyTasks) : [],
      weeklyTasks: query?.taskType === undefined || query.taskType === TaskType.WEEKLY
        ? filterTasks(task.weeklyTasks) : [],
      achievementTasks: query?.taskType === undefined || query.taskType === TaskType.ACHIEVEMENT
        ? filterTasks(task.achievementTasks) : [],
      newbieTasks: query?.taskType === undefined || query.taskType === TaskType.NEWBIE
        ? filterTasks(task.newbieTasks) : [],
      eventTasks: query?.taskType === undefined || query.taskType === TaskType.EVENT
        ? filterTasks(task.eventTasks) : [],
      mainTasks: query?.taskType === undefined || query.taskType === TaskType.MAIN
        ? filterTasks(task.mainTasks) : [],
      pendingRewards: task.pendingRewards,
      dailyProgress: task.dailyProgress,
      needsDailyRefresh: task.needsDailyRefresh,
      needsWeeklyRefresh: task.needsWeeklyRefresh,
    };

    return XResultUtils.ok(responseData);
  }

  /**
   * 更新任务进度
   * 已适配Result模式：涉及多重验证+数据更新+保存，使用executeBusinessOperation包装
   */
  async updateTaskProgress(characterId: string, serverId: string, updateDto: UpdateTaskProgressDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId || !serverId || !updateDto) {
        return XResultUtils.error('参数不完整：characterId、serverId和updateDto不能为空', 'INVALID_PARAMETERS');
      }

      if (!updateDto.taskId || updateDto.value === undefined || updateDto.value === null) {
        return XResultUtils.error('任务ID和进度值不能为空', 'INVALID_TASK_UPDATE_DATA');
      }

      if (updateDto.value < 0) {
        return XResultUtils.error('任务进度值不能为负数', 'INVALID_PROGRESS_VALUE');
      }

      // 1. 获取任务数据
      const taskResult = await this.taskRepository.getOrCreateTask(characterId, serverId);
      if (XResultUtils.isFailure(taskResult)) {
        return XResultUtils.error(`获取任务数据失败: ${taskResult.message}`, taskResult.code);
      }

      const task = taskResult.data;
      if (!task) {
        return XResultUtils.error('任务数据不存在', 'TASK_DATA_NOT_FOUND');
      }

      // 2. 更新任务进度
      const success = task.updateTaskProgress(updateDto.taskId, updateDto.value);
      if (!success) {
        return XResultUtils.failure('任务不存在或已完成', ErrorCode.TASK_NOT_FOUND, {
          taskId: updateDto.taskId,
          value: updateDto.value
        });
      }

      // 3. 强制完成任务
      if (updateDto.forceComplete) {
        task.completeTask(updateDto.taskId);
      }

      // 4. 保存数据
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await task.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存任务数据失败: ${saveResult.message}`, saveResult.code);
      }

      const updatedTask = saveResult.data;
      const taskProgress = updatedTask.getTaskById(updateDto.taskId);

      if (!taskProgress) {
        return XResultUtils.error('更新后的任务进度不存在', 'UPDATED_TASK_NOT_FOUND');
      }

      this.logger.log(`任务进度更新成功: ${characterId}, 任务: ${updateDto.taskId}, 进度: ${updateDto.value}`);

      const responseData = {
        success: true,
        message: '任务进度更新成功',
        taskProgress: this.toTaskProgressDto(taskProgress),
        isCompleted: taskProgress.status === TaskStatus.COMPLETED,
        operationTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'update_task_progress',
      metadata: { characterId, serverId, taskId: updateDto.taskId, value: updateDto.value }
    });
  }

  /**
   * 批量更新任务进度
   * 已适配Result模式：涉及多重验证+批量数据更新+保存，使用executeBusinessOperation包装
   */
  async batchUpdateTaskProgress(characterId: string, serverId: string, batchDto: BatchUpdateTaskProgressDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId || !serverId || !batchDto) {
        return XResultUtils.error('参数不完整：characterId、serverId和batchDto不能为空', 'INVALID_PARAMETERS');
      }

      if (!batchDto.updates || !Array.isArray(batchDto.updates) || batchDto.updates.length === 0) {
        return XResultUtils.error('批量更新数据不能为空', 'INVALID_BATCH_UPDATES');
      }

      // 验证每个更新项
      for (const update of batchDto.updates) {
        if (!update.taskId || update.value === undefined || update.value === null) {
          return XResultUtils.error('批量更新中存在无效的任务数据', 'INVALID_TASK_UPDATE_IN_BATCH');
        }
        if (update.value < 0) {
          return XResultUtils.error('任务进度值不能为负数', 'INVALID_PROGRESS_VALUE');
        }
      }

      // 1. 获取任务数据
      const taskResult = await this.taskRepository.getOrCreateTask(characterId, serverId);
      if (XResultUtils.isFailure(taskResult)) {
        return XResultUtils.error(`获取任务数据失败: ${taskResult.message}`, taskResult.code);
      }

      const task = taskResult.data;
      if (!task) {
        return XResultUtils.error('任务数据不存在', 'TASK_DATA_NOT_FOUND');
      }

      // 2. 批量更新任务进度
      const results: any[] = [];

      for (const update of batchDto.updates) {
        const success = task.updateTaskProgress(update.taskId, update.value);
        if (success) {
          if (update.forceComplete) {
            task.completeTask(update.taskId);
          }
          const taskProgress = task.getTaskById(update.taskId);
          if (taskProgress) {
            results.push({
              taskId: update.taskId,
              success: true,
              taskProgress: this.toTaskProgressDto(taskProgress),
              isCompleted: taskProgress.status === TaskStatus.COMPLETED,
            });
          } else {
            results.push({
              taskId: update.taskId,
              success: false,
              error: '更新后任务进度不存在',
            });
          }
        } else {
          results.push({
            taskId: update.taskId,
            success: false,
            error: '任务不存在或已完成',
          });
        }
      }

      // 3. 保存数据
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await task.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存任务数据失败: ${saveResult.message}`, saveResult.code);
      }

      const successCount = results.filter(r => r.success).length;
      this.logger.log(`批量更新任务进度成功: ${characterId}, 总数: ${batchDto.updates.length}, 成功: ${successCount}`);

      const responseData = {
        success: true,
        message: '批量更新任务进度完成',
        results,
        totalUpdated: successCount,
        totalFailed: results.length - successCount,
        operationTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'batch_update_task_progress',
      metadata: { characterId, serverId, updateCount: batchDto.updates.length }
    });
  }

  /**
   * 领取任务奖励
   * 已适配Result模式：涉及多重验证+奖励发放+数据保存，使用executeBusinessOperation包装
   */
  async claimTaskReward(characterId: string, serverId: string, claimDto: ClaimTaskRewardDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId || !serverId || !claimDto) {
        return XResultUtils.error('参数不完整：characterId、serverId和claimDto不能为空', 'INVALID_PARAMETERS');
      }

      if (!claimDto.taskId) {
        return XResultUtils.error('任务ID不能为空', 'INVALID_TASK_ID');
      }

      // 1. 获取任务数据
      const taskResult = await this.taskRepository.getOrCreateTask(characterId, serverId);
      if (XResultUtils.isFailure(taskResult)) {
        return XResultUtils.error(`获取任务数据失败: ${taskResult.message}`, taskResult.code);
      }

      const task = taskResult.data;
      if (!task) {
        return XResultUtils.error('任务数据不存在', 'TASK_DATA_NOT_FOUND');
      }

      // 2. 领取任务奖励
      const rewards = task.claimTaskReward(claimDto.taskId);
      if (!rewards) {
        return XResultUtils.failure('任务未完成或奖励已领取', ErrorCode.TASK_NOT_COMPLETED, {
          taskId: claimDto.taskId
        });
      }

      // 3. 发放奖励到玩家背包（调用微服务）
      const rewardDistributionResult = await this.distributeTaskRewards(characterId, serverId, rewards);
      if (XResultUtils.isFailure(rewardDistributionResult)) {
        this.logger.warn(`发放任务奖励失败: ${characterId}, 任务: ${claimDto.taskId}, ${rewardDistributionResult.message}`);
        // 奖励发放失败不影响领取状态，但需要记录日志
      }

      // 4. 保存数据
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await task.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存任务数据失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`任务奖励领取成功: ${characterId}, 任务: ${claimDto.taskId}`);

      const responseData = {
        success: true,
        message: '任务奖励领取成功',
        rewards,
        taskId: claimDto.taskId,
        rewardDistributed: XResultUtils.isSuccess(rewardDistributionResult),
        claimTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'claim_task_reward',
      metadata: { characterId, serverId, taskId: claimDto.taskId }
    });
  }

  /**
   * 刷新任务
   * 已适配Result模式：涉及多重验证+任务刷新+配置加载，使用executeBusinessOperation包装
   */
  async refreshTasks(characterId: string, serverId: string, refreshDto: RefreshTaskDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId || !serverId || !refreshDto) {
        return XResultUtils.error('参数不完整：characterId、serverId和refreshDto不能为空', 'INVALID_PARAMETERS');
      }

      if (!refreshDto.taskType) {
        return XResultUtils.error('任务类型不能为空', 'INVALID_TASK_TYPE');
      }

      // 1. 获取任务数据
      const taskResult = await this.taskRepository.getOrCreateTask(characterId, serverId);
      if (XResultUtils.isFailure(taskResult)) {
        return XResultUtils.error(`获取任务数据失败: ${taskResult.message}`, taskResult.code);
      }

      const task = taskResult.data;
      if (!task) {
        return XResultUtils.error('任务数据不存在', 'TASK_DATA_NOT_FOUND');
      }

      // 2. 检查是否需要刷新
      let needsRefresh = false;

      switch (refreshDto.taskType) {
        case TaskType.DAILY:
          needsRefresh = refreshDto.forceRefresh || task.needsDailyRefresh;
          if (needsRefresh) {
            task.refreshDailyTasks();
            // TODO: 从配置加载新的每日任务
            const loadResult = await this.loadDailyTasksFromConfig(characterId, serverId, task);
            if (XResultUtils.isFailure(loadResult)) {
              this.logger.warn(`加载每日任务配置失败: ${loadResult.message}`);
            }
          }
          break;
        case TaskType.WEEKLY:
          needsRefresh = refreshDto.forceRefresh || task.needsWeeklyRefresh;
          if (needsRefresh) {
            task.refreshWeeklyTasks();
            // TODO: 从配置加载新的每周任务
            const loadResult = await this.loadWeeklyTasksFromConfig(characterId, serverId, task);
            if (XResultUtils.isFailure(loadResult)) {
              this.logger.warn(`加载每周任务配置失败: ${loadResult.message}`);
            }
          }
          break;
        default:
          return XResultUtils.error('不支持的任务类型', 'UNSUPPORTED_TASK_TYPE');
      }

      // 3. 保存数据
      if (needsRefresh) {
        const saveResult = await RepositoryResultWrapper.wrap(async () => {
          return await task.save();
        });
        if (XResultUtils.isFailure(saveResult)) {
          return XResultUtils.error(`保存任务数据失败: ${saveResult.message}`, saveResult.code);
        }

        this.logger.log(`任务刷新成功: ${characterId}, 类型: ${refreshDto.taskType}`);
      }

      const responseData = {
        success: true,
        message: needsRefresh ? '任务刷新成功' : '任务无需刷新',
        refreshed: needsRefresh,
        taskType: refreshDto.taskType,
        refreshTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'refresh_tasks',
      metadata: { characterId, serverId, taskType: refreshDto.taskType, forceRefresh: refreshDto.forceRefresh }
    });
  }

  /**
   * 添加新任务
   * 已适配Result模式：涉及多重验证+任务添加+数据保存，使用executeBusinessOperation包装
   */
  async addTask(characterId: string, serverId: string, addDto: AddTaskDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId || !serverId || !addDto) {
        return XResultUtils.error('参数不完整：characterId、serverId和addDto不能为空', 'INVALID_PARAMETERS');
      }

      if (!addDto.taskId || !addDto.taskType) {
        return XResultUtils.error('任务ID和任务类型不能为空', 'INVALID_TASK_DATA');
      }

      if (addDto.targetValue === undefined || addDto.targetValue === null || addDto.targetValue <= 0) {
        return XResultUtils.error('目标值必须大于0', 'INVALID_TARGET_VALUE');
      }

      // 1. 获取任务数据
      const taskResult = await this.taskRepository.getOrCreateTask(characterId, serverId);
      if (XResultUtils.isFailure(taskResult)) {
        return XResultUtils.error(`获取任务数据失败: ${taskResult.message}`, taskResult.code);
      }

      const task = taskResult.data;
      if (!task) {
        return XResultUtils.error('任务数据不存在', 'TASK_DATA_NOT_FOUND');
      }

      // 2. 添加新任务
      const success = task.addNewTask(addDto.taskType, {
        taskId: addDto.taskId,
        targetValue: addDto.targetValue,
        description: addDto.description,
        rewards: addDto.rewards,
        expireTime: addDto.expireTime,
      });

      if (!success) {
        return XResultUtils.failure('任务添加失败，可能已存在相同任务', 'TASK_ADD_FAILED', {
          taskId: addDto.taskId,
          taskType: addDto.taskType
        });
      }

      // 3. 保存数据
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await task.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存任务数据失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`新任务添加成功: ${characterId}, 任务: ${addDto.taskId}, 类型: ${addDto.taskType}`);

      const responseData = {
        success: true,
        message: '新任务添加成功',
        taskId: addDto.taskId,
        taskType: addDto.taskType,
        targetValue: addDto.targetValue,
        addTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'add_task',
      metadata: { characterId, serverId, taskId: addDto.taskId, taskType: addDto.taskType }
    });
  }

  /**
   * 获取任务统计
   * 已适配Result模式：简单的Repository调用，直接返回结果
   */
  async getTaskStats(query: TaskStatsDto): Promise<XResult<TaskStatsResponseDto>> {
    // 参数验证
    if (!query || !query.characterId) {
      return XResultUtils.error('查询参数不完整：characterId不能为空', 'INVALID_QUERY_PARAMETERS');
    }

    if (query.days !== undefined && query.days <= 0) {
      return XResultUtils.error('天数必须大于0', 'INVALID_DAYS_PARAMETER');
    }

    const statsResult = await this.taskRepository.getTaskStats(query.characterId, query.days, query.taskType);
    if (XResultUtils.isFailure(statsResult)) {
      return XResultUtils.error(`获取任务统计失败: ${statsResult.message}`, statsResult.code);
    }

    return XResultUtils.ok(statsResult.data);
  }

  /**
   * 批量刷新任务（定时任务用）
   * 已适配Result模式：涉及多步骤Repository操作，使用executeBusinessOperation包装
   */
  async batchRefreshTasks(taskType: TaskType): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!taskType) {
        return XResultUtils.error('任务类型不能为空', 'INVALID_TASK_TYPE');
      }

      // 1. 查找需要刷新的任务
      const tasksNeedingRefreshResult = await this.taskRepository.findTasksNeedingRefresh(taskType);
      if (XResultUtils.isFailure(tasksNeedingRefreshResult)) {
        return XResultUtils.error(`查找需要刷新的任务失败: ${tasksNeedingRefreshResult.message}`, tasksNeedingRefreshResult.code);
      }

      const tasksNeedingRefresh = tasksNeedingRefreshResult.data;

      if (!tasksNeedingRefresh || tasksNeedingRefresh.length === 0) {
        const responseData = {
          refreshed: 0,
          taskType,
          message: '没有需要刷新的任务'
        };
        return XResultUtils.ok(responseData);
      }

      // 2. 批量刷新任务
      const characterIds = tasksNeedingRefresh.map(task => task.characterId);
      const batchRefreshResult = await this.taskRepository.batchRefreshTasks(characterIds, taskType);
      if (XResultUtils.isFailure(batchRefreshResult)) {
        return XResultUtils.error(`批量刷新任务失败: ${batchRefreshResult.message}`, batchRefreshResult.code);
      }

      this.logger.log(`批量刷新任务完成: ${tasksNeedingRefresh.length}个任务, 类型: ${taskType}`);

      const responseData = {
        refreshed: tasksNeedingRefresh.length,
        taskType,
        result: batchRefreshResult.data,
        operationTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'batch_refresh_tasks',
      metadata: { taskType }
    });
  }

  /**
   * 获取任务排行榜
   * 已适配Result模式：简单的Repository调用，直接返回结果
   */
  async getTaskLeaderboard(serverId: string, taskType?: TaskType, limit: number = 100): Promise<XResult<any>> {
    // 参数验证
    if (!serverId) {
      return XResultUtils.error('服务器ID不能为空', 'INVALID_SERVER_ID');
    }

    if (limit <= 0 || limit > 1000) {
      return XResultUtils.error('排行榜限制数量必须在1-1000之间', 'INVALID_LIMIT');
    }

    const leaderboardResult = await this.taskRepository.getTaskLeaderboard(serverId, taskType, limit);
    if (XResultUtils.isFailure(leaderboardResult)) {
      return XResultUtils.error(`获取任务排行榜失败: ${leaderboardResult.message}`, leaderboardResult.code);
    }

    return XResultUtils.ok(leaderboardResult.data);
  }

  // ==================== 私有方法 - 已适配Result模式 ====================

  /**
   * 转换为任务进度DTO
   * 纯数据转换函数，保持原有简洁性，不使用Result模式包装
   */
  private toTaskProgressDto(taskProgress: TaskProgress): any {
    if (!taskProgress) {
      return null;
    }

    return {
      taskId: taskProgress.taskId,
      currentValue: taskProgress.currentValue,
      targetValue: taskProgress.targetValue,
      status: taskProgress.status,
      startTime: taskProgress.startTime,
      completeTime: taskProgress.completeTime,
      claimTime: taskProgress.claimTime,
      rewards: taskProgress.rewards,
      description: taskProgress.description,
      expireTime: taskProgress.expireTime,
      canClaim: taskProgress.canClaim,
      progressPercent: taskProgress.progressPercent,
      isExpired: taskProgress.isExpired,
    };
  }

  // ==================== 缺失功能补充 ====================

  /**
   * 触发任务进度更新
   * 已适配Result模式：涉及复杂业务逻辑+多重验证+数据保存，使用executeBusinessOperation包装
   * 对应old项目中的triggerTask方法 - 这是最核心的任务系统功能
   */
  async triggerTask(
    characterId: string,
    serverId: string,
    triggerType: number,
    arg1?: any,
    arg2?: any,
    arg3?: any,
    arg4?: any
  ): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId || !serverId) {
        return XResultUtils.error('参数不完整：characterId和serverId不能为空', 'INVALID_PARAMETERS');
      }

      if (!triggerType || triggerType <= 0) {
        return XResultUtils.error('触发类型无效', 'INVALID_TRIGGER_TYPE');
      }

      // 1. 获取任务数据
      const taskResult = await this.taskRepository.getOrCreateTask(characterId, serverId);
      if (XResultUtils.isFailure(taskResult)) {
        return XResultUtils.error(`获取任务数据失败: ${taskResult.message}`, taskResult.code);
      }

      const task = taskResult.data;
      if (!task) {
        return XResultUtils.error('任务数据不存在', 'TASK_DATA_NOT_FOUND');
      }

      // 2. 根据触发类型处理不同的任务逻辑
      let isUpdated = false;
      const updatedTasks: any[] = [];

      switch (triggerType) {
        case 1: // SEARCH_HERO - 搜索球员
          const searchResult = await this.handleSearchHeroTask(task, arg1, arg2, arg3);
          isUpdated = XResultUtils.isSuccess(searchResult) ? searchResult.data : false;
          break;
        case 2: // SIGN_HERO - 签约球员
          const signResult = await this.handleSignHeroTask(task, arg1, arg2, arg3);
          isUpdated = XResultUtils.isSuccess(signResult) ? signResult.data : false;
          break;
        case 3: // AUTOMATIC_DISPOSAL - 自动处理
          const disposalResult = await this.handleAutomaticDisposalTask(task, arg1, arg2, arg3);
          isUpdated = XResultUtils.isSuccess(disposalResult) ? disposalResult.data : false;
          break;
        case 4: // LEAGUE_COPY - 联赛副本
          const leagueResult = await this.handleLeagueCopyTask(task, arg1, arg2, arg3);
          isUpdated = XResultUtils.isSuccess(leagueResult) ? leagueResult.data : false;
          break;
        case 5: // HERO_TRAIN - 球员训练
          const trainResult = await this.handleHeroTrainTask(task, arg1, arg2, arg3);
          isUpdated = XResultUtils.isSuccess(trainResult) ? trainResult.data : false;
          break;
        case 6: // USE_ITEM - 使用道具
          const itemResult = await this.handleUseItemTask(task, arg1, arg2, arg3);
          isUpdated = XResultUtils.isSuccess(itemResult) ? itemResult.data : false;
          break;
        case 7: // GET_MONEY - 获得金币
          const moneyResult = await this.handleGetMoneyTask(task, arg1, arg2, arg3);
          isUpdated = XResultUtils.isSuccess(moneyResult) ? moneyResult.data : false;
          break;
        case 8: // STRENGTH_UP - 实力提升
          const strengthResult = await this.handleStrengthUpTask(task, arg1, arg2, arg3);
          isUpdated = XResultUtils.isSuccess(strengthResult) ? strengthResult.data : false;
          break;
        case 9: // JOIN_WORLD_CUP - 参加世界杯
          const worldCupResult = await this.handleJoinWorldCupTask(task, arg1, arg2, arg3);
          isUpdated = XResultUtils.isSuccess(worldCupResult) ? worldCupResult.data : false;
          break;
        case 10: // PLAYER_LEVEL - 玩家等级
          const levelResult = await this.handleCharacterLevelTask(task, arg1, arg2, arg3);
          isUpdated = XResultUtils.isSuccess(levelResult) ? levelResult.data : false;
          break;
        case 11: // JOIN_GAME_HUB - 进入游戏圈子
          const hubResult = await this.handleJoinGameHubTask(task, arg1, arg2, arg3);
          isUpdated = XResultUtils.isSuccess(hubResult) ? hubResult.data : false;
          break;
        default:
          this.logger.warn(`未知的任务触发类型: ${triggerType}`);
          return XResultUtils.error('未知的任务触发类型', 'UNKNOWN_TRIGGER_TYPE');
      }

      // 3. 保存数据
      if (isUpdated) {
        const saveResult = await RepositoryResultWrapper.wrap(async () => {
          return await task.save();
        });
        if (XResultUtils.isFailure(saveResult)) {
          return XResultUtils.error(`保存任务数据失败: ${saveResult.message}`, saveResult.code);
        }

        this.logger.log(`任务触发成功: ${characterId}, 类型: ${triggerType}, 参数: ${arg1}, ${arg2}, ${arg3}`);
      }

      const responseData = {
        success: isUpdated,
        message: isUpdated ? '任务触发成功' : '没有任务需要更新',
        triggerType,
        updatedTasks,
        operationTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'trigger_task',
      metadata: { characterId, serverId, triggerType, arg1, arg2, arg3 }
    });
  }

  /**
   * 删除任务
   * 已适配Result模式：涉及多重验证+任务删除+新任务添加，使用executeBusinessOperation包装
   * 对应old项目中的delTask方法
   */
  async deleteTask(characterId: string, serverId: string, taskType: TaskType, taskId: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId || !serverId || !taskType || taskId === undefined || taskId === null) {
        return XResultUtils.error('参数不完整：所有参数都不能为空', 'INVALID_PARAMETERS');
      }

      if (taskId <= 0) {
        return XResultUtils.error('任务ID必须大于0', 'INVALID_TASK_ID');
      }

      // 1. 获取任务数据
      const taskResult = await this.taskRepository.getOrCreateTask(characterId, serverId);
      if (XResultUtils.isFailure(taskResult)) {
        return XResultUtils.error(`获取任务数据失败: ${taskResult.message}`, taskResult.code);
      }

      const task = taskResult.data;
      if (!task) {
        return XResultUtils.error('任务数据不存在', 'TASK_DATA_NOT_FOUND');
      }

      // 2. 查找要删除的任务
      const tasks = task.getTasksByType(taskType);
      const taskIndex = tasks.findIndex(t => t.taskId === taskId);

      if (taskIndex === -1) {
        return XResultUtils.failure('任务不存在', ErrorCode.TASK_NOT_FOUND, {
          taskId,
          taskType
        });
      }

      // 3. 删除任务
      tasks.splice(taskIndex, 1);

      // 4. 如果是日常任务或周任务，添加新任务
      let newTaskAdded = false;
      if (taskType === TaskType.DAILY || taskType === TaskType.WEEKLY) {
        const nextTaskIdResult = await this.getNextTaskId(taskType, taskId);
        if (XResultUtils.isSuccess(nextTaskIdResult) && nextTaskIdResult.data !== 0) {
          const addResult = await this.addNewTaskToList(task, taskType, nextTaskIdResult.data);
          newTaskAdded = XResultUtils.isSuccess(addResult);
          if (!newTaskAdded) {
            this.logger.warn(`添加新任务失败: ${addResult.message}`);
          }
        }
      }

      // 5. 保存数据
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await task.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存任务数据失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`任务删除成功: ${characterId}, 类型: ${taskType}, 任务: ${taskId}`);

      const responseData = {
        success: true,
        message: '任务删除成功',
        deletedTaskId: taskId,
        taskType,
        newTaskAdded,
        operationTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'delete_task',
      metadata: { characterId, serverId, taskType, taskId }
    });
  }

  /**
   * 检查任务列表是否已满
   * 已适配Result模式：简单的业务操作，不使用executeBusinessOperation
   * 对应old项目中的_checkTaskIsFull方法
   */
  async checkTaskListFull(characterId: string, serverId: string, taskType: TaskType): Promise<XResult<boolean>> {
    // 参数验证
    if (!characterId || !serverId || !taskType) {
      return XResultUtils.error('参数不完整：characterId、serverId和taskType不能为空', 'INVALID_PARAMETERS');
    }

    // 获取任务数据
    const taskResult = await this.taskRepository.getOrCreateTask(characterId, serverId);
    if (XResultUtils.isFailure(taskResult)) {
      return XResultUtils.error(`获取任务数据失败: ${taskResult.message}`, taskResult.code);
    }

    const task = taskResult.data;
    if (!task) {
      return XResultUtils.error('任务数据不存在', 'TASK_DATA_NOT_FOUND');
    }

    const tasks = task.getTasksByType(taskType);
    const maxCapacity = this.getTaskListCapacity(taskType);
    const isFull = tasks.length >= maxCapacity;

    return XResultUtils.ok(isFull);
  }

  /**
   * 检查任务是否存在
   * 已适配Result模式：简单的业务操作，不使用executeBusinessOperation
   * 对应old项目newerTask.js中的checkIsHaveTask方法
   */
  async checkTaskExists(characterId: string, serverId: string, taskId: number): Promise<XResult<boolean>> {
    // 参数验证
    if (!characterId || !serverId || taskId === undefined || taskId === null) {
      return XResultUtils.error('参数不完整：characterId、serverId和taskId不能为空', 'INVALID_PARAMETERS');
    }

    if (taskId <= 0) {
      return XResultUtils.error('任务ID必须大于0', 'INVALID_TASK_ID');
    }

    // 获取任务数据
    const taskResult = await this.taskRepository.getOrCreateTask(characterId, serverId);
    if (XResultUtils.isFailure(taskResult)) {
      return XResultUtils.error(`获取任务数据失败: ${taskResult.message}`, taskResult.code);
    }

    const task = taskResult.data;
    if (!task) {
      return XResultUtils.error('任务数据不存在', 'TASK_DATA_NOT_FOUND');
    }

    const foundTask = task.getTaskById(taskId);
    const exists = foundTask !== null;

    return XResultUtils.ok(exists);
  }

  // ==================== 任务触发处理方法 - 已适配Result模式 ====================

  /**
   * 处理搜索球员任务
   * 已适配Result模式：返回XResult类型，增加参数验证
   */
  private async handleSearchHeroTask(task: any, arg1?: any, arg2?: any, arg3?: any): Promise<XResult<boolean>> {
    if (!task) {
      return XResultUtils.error('任务数据不能为空', 'INVALID_TASK_DATA');
    }

    // 实现搜索球员任务逻辑
    // 查找相关任务并更新进度
    const updateResult = await this.updateTasksByType(task, 1, arg1, arg2, arg3);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`更新搜索球员任务失败: ${updateResult.message}`, updateResult.code);
    }

    return XResultUtils.ok(updateResult.data);
  }

  /**
   * 处理签约球员任务
   * 已适配Result模式：返回XResult类型，增加参数验证
   */
  private async handleSignHeroTask(task: any, arg1?: any, arg2?: any, arg3?: any): Promise<XResult<boolean>> {
    if (!task) {
      return XResultUtils.error('任务数据不能为空', 'INVALID_TASK_DATA');
    }

    const updateResult = await this.updateTasksByType(task, 2, arg1, arg2, arg3);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`更新签约球员任务失败: ${updateResult.message}`, updateResult.code);
    }

    return XResultUtils.ok(updateResult.data);
  }

  /**
   * 处理自动处理任务
   * 已适配Result模式：返回XResult类型，增加参数验证
   */
  private async handleAutomaticDisposalTask(task: any, arg1?: any, arg2?: any, arg3?: any): Promise<XResult<boolean>> {
    if (!task) {
      return XResultUtils.error('任务数据不能为空', 'INVALID_TASK_DATA');
    }

    const updateResult = await this.updateTasksByType(task, 3, arg1, arg2, arg3);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`更新自动处理任务失败: ${updateResult.message}`, updateResult.code);
    }

    return XResultUtils.ok(updateResult.data);
  }

  /**
   * 处理联赛副本任务
   * 已适配Result模式：返回XResult类型，增加参数验证
   */
  private async handleLeagueCopyTask(task: any, arg1?: any, arg2?: any, arg3?: any): Promise<XResult<boolean>> {
    if (!task) {
      return XResultUtils.error('任务数据不能为空', 'INVALID_TASK_DATA');
    }

    // 实现联赛副本任务逻辑，包括星级检查
    const updateResult = await this.updateTasksByType(task, 4, arg1, arg2, arg3);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`更新联赛副本任务失败: ${updateResult.message}`, updateResult.code);
    }

    return XResultUtils.ok(updateResult.data);
  }

  /**
   * 处理球员训练任务
   * 已适配Result模式：返回XResult类型，增加参数验证
   */
  private async handleHeroTrainTask(task: any, arg1?: any, arg2?: any, arg3?: any): Promise<XResult<boolean>> {
    if (!task) {
      return XResultUtils.error('任务数据不能为空', 'INVALID_TASK_DATA');
    }

    const updateResult = await this.updateTasksByType(task, 5, arg1, arg2, arg3);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`更新球员训练任务失败: ${updateResult.message}`, updateResult.code);
    }

    return XResultUtils.ok(updateResult.data);
  }

  /**
   * 处理使用道具任务
   * 已适配Result模式：返回XResult类型，增加参数验证
   */
  private async handleUseItemTask(task: any, arg1?: any, arg2?: any, arg3?: any): Promise<XResult<boolean>> {
    if (!task) {
      return XResultUtils.error('任务数据不能为空', 'INVALID_TASK_DATA');
    }

    // 实现使用道具任务逻辑，检查道具ID
    const updateResult = await this.updateTasksByType(task, 6, arg1, arg2, arg3);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`更新使用道具任务失败: ${updateResult.message}`, updateResult.code);
    }

    return XResultUtils.ok(updateResult.data);
  }

  /**
   * 处理获得金币任务
   * 已适配Result模式：返回XResult类型，增加参数验证
   */
  private async handleGetMoneyTask(task: any, arg1?: any, arg2?: any, arg3?: any): Promise<XResult<boolean>> {
    if (!task) {
      return XResultUtils.error('任务数据不能为空', 'INVALID_TASK_DATA');
    }

    const updateResult = await this.updateTasksByType(task, 7, arg1, arg2, arg3);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`更新获得金币任务失败: ${updateResult.message}`, updateResult.code);
    }

    return XResultUtils.ok(updateResult.data);
  }

  /**
   * 处理实力提升任务
   * 已适配Result模式：返回XResult类型，增加参数验证
   */
  private async handleStrengthUpTask(task: any, arg1?: any, arg2?: any, arg3?: any): Promise<XResult<boolean>> {
    if (!task) {
      return XResultUtils.error('任务数据不能为空', 'INVALID_TASK_DATA');
    }

    // 实现实力提升任务逻辑，检查提升数值
    const updateResult = await this.updateTasksByType(task, 8, arg1, arg2, arg3);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`更新实力提升任务失败: ${updateResult.message}`, updateResult.code);
    }

    return XResultUtils.ok(updateResult.data);
  }

  /**
   * 处理参加世界杯任务
   * 已适配Result模式：返回XResult类型，增加参数验证
   */
  private async handleJoinWorldCupTask(task: any, arg1?: any, arg2?: any, arg3?: any): Promise<XResult<boolean>> {
    if (!task) {
      return XResultUtils.error('任务数据不能为空', 'INVALID_TASK_DATA');
    }

    const updateResult = await this.updateTasksByType(task, 9, arg1, arg2, arg3);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`更新参加世界杯任务失败: ${updateResult.message}`, updateResult.code);
    }

    return XResultUtils.ok(updateResult.data);
  }

  /**
   * 处理玩家等级任务
   * 已适配Result模式：返回XResult类型，增加参数验证
   */
  private async handleCharacterLevelTask(task: any, arg1?: any, arg2?: any, arg3?: any): Promise<XResult<boolean>> {
    if (!task) {
      return XResultUtils.error('任务数据不能为空', 'INVALID_TASK_DATA');
    }

    // 实现玩家等级任务逻辑，检查等级数值
    const updateResult = await this.updateTasksByType(task, 10, arg1, arg2, arg3);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`更新玩家等级任务失败: ${updateResult.message}`, updateResult.code);
    }

    return XResultUtils.ok(updateResult.data);
  }

  /**
   * 处理进入游戏圈子任务
   * 已适配Result模式：返回XResult类型，增加参数验证
   */
  private async handleJoinGameHubTask(task: any, arg1?: any, arg2?: any, arg3?: any): Promise<XResult<boolean>> {
    if (!task) {
      return XResultUtils.error('任务数据不能为空', 'INVALID_TASK_DATA');
    }

    const updateResult = await this.updateTasksByType(task, 11, arg1, arg2, arg3);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`更新进入游戏圈子任务失败: ${updateResult.message}`, updateResult.code);
    }

    return XResultUtils.ok(updateResult.data);
  }

  /**
   * 根据类型更新任务进度
   * 已适配Result模式：返回XResult类型，增加错误处理和参数验证
   * 基于old项目: 任务触发和进度更新逻辑
   */
  private async updateTasksByType(task: any, triggerType: number, arg1?: any, arg2?: any, arg3?: any): Promise<XResult<boolean>> {
    if (!task) {
      return XResultUtils.error('任务数据不能为空', 'INVALID_TASK_DATA');
    }

    if (triggerType === undefined || triggerType === null || triggerType <= 0) {
      return XResultUtils.error('触发类型无效', 'INVALID_TRIGGER_TYPE');
    }

    try {
      let isUpdated = false;

      // 获取所有任务列表
      const allTasks = [
        ...(task.dailyTasks || []),
        ...(task.weeklyTasks || []),
        ...(task.achievementTasks || []),
        ...(task.newbieTasks || []),
        ...(task.eventTasks || []),
        ...(task.mainTasks || []),
      ];

      // 查找匹配的任务（基于old项目：根据触发类型查找）
      for (const taskItem of allTasks) {
        if (!taskItem || taskItem.status === TaskStatus.CLAIMED) {
          continue; // 已领取的任务跳过
        }

        // 检查任务是否匹配当前触发类型
        const matchResult = await this.isTaskMatchTrigger(taskItem, triggerType, arg1, arg2, arg3);
        const isMatch = XResultUtils.isSuccess(matchResult) ? matchResult.data : false;

        if (isMatch) {
          // 更新任务进度
          const oldValue = taskItem.currentValue || 0;
          const newValue = Math.max(oldValue, arg1 || 1);
          taskItem.currentValue = newValue;

          // 检查是否完成
          if (taskItem.currentValue >= taskItem.targetValue && taskItem.status === TaskStatus.IN_PROGRESS) {
            taskItem.status = TaskStatus.COMPLETED;
            taskItem.completeTime = Date.now();
            this.logger.log(`任务完成: ${taskItem.taskId}, 进度: ${taskItem.currentValue}/${taskItem.targetValue}`);
          }

          if (taskItem.currentValue !== oldValue) {
            isUpdated = true;
            this.logger.debug(`任务进度更新: ${taskItem.taskId}, ${oldValue} -> ${taskItem.currentValue}`);
          }
        }
      }

      if (isUpdated) {
        task.lastUpdateTime = Date.now();
      }

      return XResultUtils.ok(isUpdated);
    } catch (error) {
      return XResultUtils.error(`更新任务进度异常: ${error.message}`, 'UPDATE_TASKS_BY_TYPE_ERROR');
    }
  }

  /**
   * 获取任务列表容量
   */
  private getTaskListCapacity(taskType: TaskType): number {
    // TODO: 从配置表获取
    switch (taskType) {
      case TaskType.DAILY:
        return 5;
      case TaskType.WEEKLY:
        return 3;
      case TaskType.ACHIEVEMENT:
        return 50;
      case TaskType.NEWBIE:
        return 20;
      case TaskType.EVENT:
        return 10;
      case TaskType.MAIN:
        return 30;
      default:
        return 10;
    }
  }

  /**
   * 获取下一个任务ID
   */
  private async getNextTaskId(taskType: TaskType, currentTaskId: number): Promise<XResult<number>> {
    // TODO: 从配置表获取下一个任务ID
    return 0;
  }

  /**
   * 添加新任务到列表
   */
  private async addNewTaskToList(task: any, taskType: TaskType, taskId: number): Promise<XResult<void>> {
    // TODO: 实现添加新任务逻辑
    this.logger.log(`添加新任务: 类型 ${taskType}, ID ${taskId}`);
  }

  /**
   * 检查任务是否匹配触发条件
   * 基于old项目: 任务触发条件匹配逻辑
   */
  private isTaskMatchTrigger(taskItem: any, triggerType: number, arg1?: any, arg2?: any, arg3?: any): boolean {
    // 基于old项目：任务的触发类型必须匹配
    if (taskItem.triggerType !== triggerType) {
      return false;
    }

    // 根据不同的触发类型进行额外的条件检查
    switch (triggerType) {
      case 1: // 搜索球员
        return true; // 所有搜索球员任务都匹配

      case 2: // 签约球员
        // 检查球员品质等条件
        return !taskItem.arg1 || taskItem.arg1 <= (arg1 || 0);

      case 4: // 联赛副本
        // 检查星级条件
        return !taskItem.arg2 || taskItem.arg2 <= (arg2 || 0);

      case 6: // 使用道具
        // 检查道具ID
        return !taskItem.arg1 || taskItem.arg1 === arg1;

      case 8: // 实力提升
        // 检查提升数值
        return !taskItem.arg1 || taskItem.arg1 <= (arg1 || 0);

      case 10: // 玩家等级
        // 检查等级数值
        return !taskItem.arg1 || taskItem.arg1 <= (arg1 || 0);

      default:
        return true; // 其他类型默认匹配
    }
  }

  // ==================== 活动任务功能补充 ====================

  /**
   * 更新活动任务进度
   * 对应old项目act.js中的updateTaskProgress方法 - 缺失的核心功能
   */
  async updateActivityTaskProgress(
    characterId: string,
    serverId: string,
    activityId: string,
    taskId: number,
    progress: number
  ): Promise<XResult<any>> {
    try {
      const task = await this.taskRepository.getOrCreateTask(characterId, serverId);

      // 查找活动任务
      const activityTasks = task.getTasksByType(TaskType.EVENT);
      const activityTask = activityTasks.find(t =>
        t.taskId === taskId && t.activityId === activityId
      );

      if (!activityTask) {
        this.logger.warn(`活动任务不存在: ${characterId}, 活动: ${activityId}, 任务: ${taskId}`);
        return { success: false, error: 'Activity task not found' };
      }

      // 更新进度
      const oldProgress = activityTask.currentProgress;
      activityTask.currentProgress = Math.min(progress, activityTask.targetProgress);

      // 检查是否完成
      const isCompleted = activityTask.currentProgress >= activityTask.targetProgress;
      if (isCompleted && activityTask.status === TaskStatus.IN_PROGRESS) {
        activityTask.status = TaskStatus.COMPLETED;
        activityTask.completeTime = Date.now();
      }

      await task.save();

      this.logger.log(`活动任务进度更新: ${characterId}, 活动: ${activityId}, 任务: ${taskId}, 进度: ${oldProgress} -> ${activityTask.currentProgress}`);

      return {
        success: true,
        activityId,
        taskId,
        oldProgress,
        newProgress: activityTask.currentProgress,
        isCompleted,
        canClaimReward: isCompleted,
      };
    } catch (error) {
      this.logger.error('更新活动任务进度失败', error);
      throw error;
    }
  }

  /**
   * 更新金牌教练任务进度
   * 对应old项目act.js中的updateGoldCoachTaskProgress方法 - 缺失的核心功能
   */
  async updateGoldCoachTaskProgress(
    characterId: string,
    serverId: string,
    coachAction: string,
    param?: any
  ): Promise<XResult<any>> {
    try {
      const task = await this.taskRepository.getOrCreateTask(characterId, serverId);

      // 查找金牌教练任务
      const coachTasks = task.getTasksByType(TaskType.EVENT).filter(t =>
        t.taskCategory === 'GOLD_COACH'
      );

      let updatedTasks = 0;

      for (const coachTask of coachTasks) {
        if (coachTask.status !== TaskStatus.IN_PROGRESS) continue;

        // 根据教练行为类型更新进度
        let progressIncrement = 0;

        switch (coachAction) {
          case 'TRAIN_PLAYER':
            if (coachTask.taskCondition === 'TRAIN_COUNT') {
              progressIncrement = 1;
            } else if (coachTask.taskCondition === 'TRAIN_LEVEL' && param?.levels) {
              progressIncrement = param.levels;
            }
            break;
          case 'SIGN_PLAYER':
            if (coachTask.taskCondition === 'SIGN_COUNT') {
              progressIncrement = 1;
            } else if (coachTask.taskCondition === 'SIGN_QUALITY' && param?.quality >= coachTask.requiredQuality) {
              progressIncrement = 1;
            }
            break;
          case 'WIN_MATCH':
            if (coachTask.taskCondition === 'WIN_COUNT') {
              progressIncrement = 1;
            } else if (coachTask.taskCondition === 'WIN_STREAK' && param?.isStreak) {
              progressIncrement = 1;
            }
            break;
          case 'FORMATION_CHANGE':
            if (coachTask.taskCondition === 'FORMATION_COUNT') {
              progressIncrement = 1;
            }
            break;
          default:
            continue;
        }

        if (progressIncrement > 0) {
          const oldProgress = coachTask.currentProgress;
          coachTask.currentProgress = Math.min(
            coachTask.currentProgress + progressIncrement,
            coachTask.targetProgress
          );

          // 检查是否完成
          if (coachTask.currentProgress >= coachTask.targetProgress && coachTask.status === TaskStatus.IN_PROGRESS) {
            coachTask.status = TaskStatus.COMPLETED;
            coachTask.completeTime = Date.now();
          }

          updatedTasks++;

          this.logger.log(`金牌教练任务更新: ${characterId}, 任务: ${coachTask.taskId}, 行为: ${coachAction}, 进度: ${oldProgress} -> ${coachTask.currentProgress}`);
        }
      }

      if (updatedTasks > 0) {
        await task.save();
      }

      return {
        success: updatedTasks > 0,
        coachAction,
        updatedTasksCount: updatedTasks,
        param,
      };
    } catch (error) {
      this.logger.error('更新金牌教练任务进度失败', error);
      throw error;
    }
  }

  /**
   * 批量更新活动任务
   * 对应old项目中的批量任务更新逻辑
   */
  async batchUpdateActivityTasks(
    characterId: string,
    serverId: string,
    updates: Array<{
      activityId: string;
      taskId: number;
      progress: number;
    }>
  ): Promise<XResult<any>> {
    try {
      const results = [];

      for (const update of updates) {
        const result = await this.updateActivityTaskProgress(
          characterId,
          serverId,
          update.activityId,
          update.taskId,
          update.progress
        );
        results.push(result);
      }

      this.logger.log(`批量更新活动任务: ${characterId}, 更新数量: ${updates.length}`);

      return {
        success: true,
        totalUpdates: updates.length,
        results,
        operationTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('批量更新活动任务失败', error);
      throw error;
    }
  }
}
