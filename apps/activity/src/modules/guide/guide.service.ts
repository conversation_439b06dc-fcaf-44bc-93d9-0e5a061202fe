/**
 * 新手引导服务 - Result模式完整适配版本
 *
 * 核心功能：
 * - 新手引导状态管理和进度跟踪
 * - 引导触发条件检查和自动触发
 * - 引导奖励发放和完成记录
 * - 引导跳过和重置功能
 *
 * Result模式适配特性：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public和private方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装复杂业务操作
 * - 标准化的错误处理和业务逻辑验证
 * - 完整的中文注释和操作日志
 *
 * 基于old项目NewerGuide业务逻辑迁移和优化
 */

import { Injectable } from '@nestjs/common';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { GuideRepository } from '@activity/common/repositories/guide.repository';
import {
  GetGuideStatusDto,
  UpdateGuideDto,
  CheckGuideTriggerDto,
  SkipGuideDto,
  ResetGuideDto,
  SetNextIdDto,
  GuideStatusResponseDto,
  GuideUpdateResponseDto,
  GuideTriggerResponseDto,
  SkipGuideResponseDto,
  ResetGuideResponseDto,
  CreateGuideDto,
  GuideInfoDto,
  TriggerGuideInfoDto
} from '@activity/common/dto/guide.dto';
import { GuideType, GuideStatus, UserGroupType, GuideTriggerType } from '@activity/common/schemas/guide.schema';
import { RepositoryResultWrapper, XResult, XResultUtils } from '@libs/common/types/result.type';
import { BaseService } from '@libs/common/service/base-service';
import { MicroserviceClientService } from '@libs/service-mesh';

// 使用DTO中的接口
type GuideInfo = GuideInfoDto;
type TriggerGuideInfo = TriggerGuideInfoDto;

// 新手引导数据接口（基于old项目NewerGuide结构）
export interface GuideData {
  uid: string;         // 玩家UID
  nextId: number;      // 下一个引导ID
  guideFinnishList: Map<number, GuideInfo>; // 完成的引导列表
  triggerList: Map<number, TriggerGuideInfo>; // 触发引导列表
  triggerFinishList: Map<number, TriggerGuideInfo>; // 完成的触发引导列表
  isSkipped: boolean;  // 是否已跳过引导
  skipTime?: number;   // 跳过时间
}

@Injectable()
export class GuideService extends BaseService {
  constructor(
    private readonly guideRepository: GuideRepository,
    microserviceClient: MicroserviceClientService,
  ) {
    super('GuideService', microserviceClient);
  }

  /**
   * 初始化引导数据
   * 已适配Result模式：简单的数据获取操作，不使用executeBusinessOperation
   * 基于old项目initByDB方法
   */
  async initGuideData(characterId: string): Promise<XResult<GuideData>> {
    // 参数验证
    if (!characterId) {
      return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
    }

    // 从数据库加载引导数据
    const guideDataResult = await this.getGuideData(characterId);
    if (XResultUtils.isFailure(guideDataResult)) {
      return XResultUtils.error(`获取引导数据失败: ${guideDataResult.message}`, guideDataResult.code);
    }

    let guideData = guideDataResult.data;
    if (!guideData) {
      // 创建新的引导数据
      const newGuideDataResult = await this.createNewGuideData(characterId);
      if (XResultUtils.isFailure(newGuideDataResult)) {
        return XResultUtils.error(`创建引导数据失败: ${newGuideDataResult.message}`, newGuideDataResult.code);
      }
      guideData = newGuideDataResult.data;
    }

    return XResultUtils.ok(guideData);
  }

  /**
   * 获取新手引导状态
   * 已适配Result模式：涉及多步骤数据处理，使用executeBusinessOperation包装
   */
  async getGuideStatus(characterId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId) {
        return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
      }

      // 1. 初始化引导数据
      const guideDataResult = await this.initGuideData(characterId);
      if (XResultUtils.isFailure(guideDataResult)) {
        return XResultUtils.error(`初始化引导数据失败: ${guideDataResult.message}`, guideDataResult.code);
      }

      const guideData = guideDataResult.data;

      // 2. 获取引导配置
      const guideConfigsResult = await this.getGuideConfigs();
      if (XResultUtils.isFailure(guideConfigsResult)) {
        return XResultUtils.error(`获取引导配置失败: ${guideConfigsResult.message}`, guideConfigsResult.code);
      }

      const guideConfigs = guideConfigsResult.data;

      // 3. 计算完成的步骤
      const completedSteps: number[] = [];
      for (const [guideId, guideInfo] of guideData.guideFinnishList) {
        if (guideInfo.status === GuideStatus.FINISH) {
          completedSteps.push(guideId);
        }
      }

      // 4. 计算总步骤数
      const totalSteps = guideConfigs.length;

      // 5. 检查是否是新手
      const isNewerResult = await this.isNewer(characterId);
      if (XResultUtils.isFailure(isNewerResult)) {
        this.logger.warn(`检查新手状态失败: ${isNewerResult.message}`);
      }

      const isNewer = XResultUtils.isSuccess(isNewerResult) ? isNewerResult.data : true;

      this.logger.log(`获取新手引导状态成功: ${characterId}, 当前步骤: ${guideData.nextId}, 完成: ${completedSteps.length}/${totalSteps}`);

      const responseData = {
        success: true,
        message: '获取引导状态成功',
        currentStep: guideData.nextId,
        completedSteps,
        totalSteps,
        isCompleted: guideData.nextId === 0 && completedSteps.length > 0,
        isNewer,
        completionRate: totalSteps > 0 ? Math.floor((completedSteps.length / totalSteps) * 100) : 0,
        isSkipped: guideData.isSkipped,
        skipTime: guideData.skipTime,
        operationTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'get_guide_status',
      metadata: { characterId }
    });
  }

  /**
   * 更新引导进度
   * 已适配Result模式：涉及复杂业务逻辑+多重验证+奖励发放，使用executeBusinessOperation包装
   * 基于old项目updateGuide方法
   */
  async updateGuide(characterId: string, guideId: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId || guideId === undefined || guideId === null) {
        return XResultUtils.error('参数不完整：characterId和guideId不能为空', 'INVALID_PARAMETERS');
      }

      if (guideId < 0) {
        return XResultUtils.error('引导ID无效', 'INVALID_GUIDE_ID');
      }

      // 1. 初始化引导数据
      const guideDataResult = await this.initGuideData(characterId);
      if (XResultUtils.isFailure(guideDataResult)) {
        return XResultUtils.error(`初始化引导数据失败: ${guideDataResult.message}`, guideDataResult.code);
      }

      const guideData = guideDataResult.data;

      // 2. 获取引导配置
      const guideConfigResult = await this.getGuideConfig(guideId);
      if (XResultUtils.isFailure(guideConfigResult)) {
        return XResultUtils.error(`获取引导配置失败: ${guideConfigResult.message}`, guideConfigResult.code);
      }

      const guideConfig = guideConfigResult.data;
      if (!guideConfig) {
        return XResultUtils.error('引导配置不存在', ErrorCode.ACTIVITY_NOT_FOUND);
      }

      // 3. 检查引导是否已完成
      const isFinishedResult = await this.checkGuideFinish(guideData, guideId);
      if (XResultUtils.isFailure(isFinishedResult)) {
        return XResultUtils.error(`检查引导完成状态失败: ${isFinishedResult.message}`, isFinishedResult.code);
      }

      if (isFinishedResult.data) {
        // 引导已完成，计算下一步
        const nextId = guideId + 1;
        const nextConfigResult = await this.getGuideConfig(nextId);

        if (XResultUtils.isSuccess(nextConfigResult) && nextConfigResult.data) {
          guideData.nextId = nextId;
        } else {
          guideData.nextId = 0; // 所有引导完成
        }

        // 保存到数据库
        const saveResult = await this.saveGuideData(guideData);
        if (XResultUtils.isFailure(saveResult)) {
          return XResultUtils.error(`保存引导数据失败: ${saveResult.message}`, saveResult.code);
        }

        const responseData = {
          success: true,
          message: '引导已完成',
          guideId,
          nextStep: guideData.nextId,
          rewards: [],
          rewardCount: 0,
          alreadyCompleted: true,
          skipped: false,
          reason: null,
          operationTime: Date.now(),
        };

        return XResultUtils.ok(responseData);
      }

      // 4. 验证引导类型（只记录硬性引导）
      if (guideConfig.GuideType !== GuideType.HARD) {
        this.logger.warn(`跳过软性引导: ${guideId}`);
        const responseData = {
          success: true,
          message: '软性引导已跳过',
          guideId,
          nextStep: guideData.nextId,
          rewards: [],
          rewardCount: 0,
          alreadyCompleted: false,
          skipped: true,
          reason: 'soft_guide',
          operationTime: Date.now(),
        };

        return XResultUtils.ok(responseData);
      }

      // 5. 检查用户组权限
      const isNewerResult = await this.isNewer(characterId);
      const isNewer = XResultUtils.isSuccess(isNewerResult) ? isNewerResult.data : true;

      if (guideConfig.UserGroup === UserGroupType.NEWER && !isNewer) {
        // 特殊ID例外
        if (guideId !== 999 && guideId !== 1000) {
          return XResultUtils.failure('非新手用户无法执行此引导', 'NOT_NEWER_USER', {
            success: false,
            message: '非新手用户无法执行此引导',
            guideId,
            nextStep: guideData.nextId,
            rewards: [],
            rewardCount: 0,
            alreadyCompleted: false,
            skipped: false,
            reason: 'not_newer_user',
            userGroup: guideConfig.UserGroup,
            isNewer,
            operationTime: Date.now(),
          });
        }
      }

      // 6. 处理触发类型的硬性引导
      if (guideData.triggerFinishList.has(guideId)) {
        const triggerInfo = guideData.triggerFinishList.get(guideId)!;
        triggerInfo.status = GuideStatus.FINISH;
      }

      // 7. 设置完成状态
      const setFinishResult = await this.setFinish(guideData, guideId);
      if (XResultUtils.isFailure(setFinishResult)) {
        return XResultUtils.error(`设置引导完成状态失败: ${setFinishResult.message}`, setFinishResult.code);
      }

      // 8. 处理奖励
      const rewardsResult = await this.processGuideRewards(characterId, guideConfig);
      if (XResultUtils.isFailure(rewardsResult)) {
        this.logger.warn(`处理引导奖励失败: ${characterId}, 引导: ${guideId}, ${rewardsResult.message}`);
      }

      const rewards = XResultUtils.isSuccess(rewardsResult) ? rewardsResult.data : [];

      // 9. 计算下一个引导ID
      let nextId = guideConfig.RearId || 0;
      if (nextId) {
        const nextConfigResult = await this.getGuideConfig(nextId);
        if (XResultUtils.isSuccess(nextConfigResult) && nextConfigResult.data) {
          const nextConfig = nextConfigResult.data;
          if (nextConfig.UserGroup === UserGroupType.NEWER && !isNewer) {
            nextId = 0;
          }
        }
      }

      guideData.nextId = nextId;

      // 10. 触发相关任务（基于old项目逻辑）
      if (guideId === 300) {
        // TODO: 触发加入游戏圈子任务
        // const taskResult = await this.triggerTask(TARGET_TYPE.JOIN_GAME_HUB);
        // if (XResultUtils.isFailure(taskResult)) {
        //   this.logger.warn(`触发游戏圈子任务失败: ${taskResult.message}`);
        // }
      }

      // 11. 保存到数据库
      const saveResult = await this.saveGuideData(guideData);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存引导数据失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`引导步骤完成: ${characterId}, 步骤: ${guideId}, 下一步: ${nextId}`);

      const responseData = {
        success: true,
        message: '引导进度更新成功',
        guideId,
        nextStep: nextId,
        rewards,
        rewardCount: rewards.length,
        alreadyCompleted: false,
        skipped: false,
        reason: null,
        operationTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'update_guide',
      metadata: { characterId, guideId }
    });
  }



  /**
   * 跳过引导
   * 已适配Result模式：简单的业务操作，不使用executeBusinessOperation
   */
  async skipGuide(characterId: string): Promise<XResult<any>> {
    // 参数验证
    if (!characterId) {
      return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
    }

    // 初始化引导数据
    const guideDataResult = await this.initGuideData(characterId);
    if (XResultUtils.isFailure(guideDataResult)) {
      return XResultUtils.error(`初始化引导数据失败: ${guideDataResult.message}`, guideDataResult.code);
    }

    const guideData = guideDataResult.data;

    // 设置跳过状态
    guideData.nextId = 0; // 表示跳过所有引导
    guideData.isSkipped = true;
    guideData.skipTime = Date.now();

    // 保存到数据库
    const saveResult = await this.saveGuideData(guideData);
    if (XResultUtils.isFailure(saveResult)) {
      return XResultUtils.error(`保存引导数据失败: ${saveResult.message}`, saveResult.code);
    }

    this.logger.log(`跳过引导成功: ${characterId}`);

    const responseData = {
      success: true,
      message: '引导已跳过',
      skipped: true,
      skippedAt: Date.now(),
    };

    return XResultUtils.ok(responseData);
  }

  /**
   * 重置引导
   * 已适配Result模式：简单的业务操作，不使用executeBusinessOperation
   */
  async resetGuide(characterId: string): Promise<XResult<any>> {
    // 参数验证
    if (!characterId) {
      return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
    }

    // 创建新的引导数据
    const newGuideDataResult = await this.createNewGuideData(characterId);
    if (XResultUtils.isFailure(newGuideDataResult)) {
      return XResultUtils.error(`创建新引导数据失败: ${newGuideDataResult.message}`, newGuideDataResult.code);
    }

    const newGuideData = newGuideDataResult.data;

    // 保存到数据库
    const saveResult = await this.saveGuideData(newGuideData);
    if (XResultUtils.isFailure(saveResult)) {
      return XResultUtils.error(`保存引导数据失败: ${saveResult.message}`, saveResult.code);
    }

    this.logger.log(`重置引导成功: ${characterId}`);

    const responseData = {
      success: true,
      message: '引导已重置',
      reset: true,
      resetAt: Date.now(),
    };

    return XResultUtils.ok(responseData);
  }

  /**
   * 设置当前引导ID为0
   * 已适配Result模式：简单的业务操作，不使用executeBusinessOperation
   * 基于old项目setCurrIdZero方法
   */
  async setCurrentIdZero(characterId: string): Promise<XResult<any>> {
    // 参数验证
    if (!characterId) {
      return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
    }

    // 初始化引导数据
    const guideDataResult = await this.initGuideData(characterId);
    if (XResultUtils.isFailure(guideDataResult)) {
      return XResultUtils.error(`初始化引导数据失败: ${guideDataResult.message}`, guideDataResult.code);
    }

    const guideData = guideDataResult.data;
    guideData.nextId = 0;

    // 保存到数据库
    const saveResult = await this.saveGuideData(guideData);
    if (XResultUtils.isFailure(saveResult)) {
      return XResultUtils.error(`保存引导数据失败: ${saveResult.message}`, saveResult.code);
    }

    this.logger.log(`设置当前引导ID为0成功: ${characterId}`);

    const responseData = {
      success: true,
      message: '当前引导ID已设置为0',
      currentId: 0,
      operationTime: Date.now(),
    };

    return XResultUtils.ok(responseData);
  }

  /**
   * 设置下一个引导ID
   * 已适配Result模式：简单的业务操作，不使用executeBusinessOperation
   * 基于old项目setNextId方法
   */
  async setNextId(characterId: string, nextId: number): Promise<XResult<any>> {
    // 参数验证
    if (!characterId || nextId === undefined || nextId === null) {
      return XResultUtils.error('参数不完整：characterId和nextId不能为空', 'INVALID_PARAMETERS');
    }

    if (nextId < 0) {
      return XResultUtils.error('引导ID不能为负数', 'INVALID_NEXT_ID');
    }

    // 初始化引导数据
    const guideDataResult = await this.initGuideData(characterId);
    if (XResultUtils.isFailure(guideDataResult)) {
      return XResultUtils.error(`初始化引导数据失败: ${guideDataResult.message}`, guideDataResult.code);
    }

    const guideData = guideDataResult.data;
    guideData.nextId = nextId;

    // 保存到数据库
    const saveResult = await this.saveGuideData(guideData);
    if (XResultUtils.isFailure(saveResult)) {
      return XResultUtils.error(`保存引导数据失败: ${saveResult.message}`, saveResult.code);
    }

    this.logger.log(`设置下一个引导ID成功: ${characterId}, nextId: ${nextId}`);

    const responseData = {
      success: true,
      message: '下一个引导ID设置成功',
      nextId,
      operationTime: Date.now(),
    };

    return XResultUtils.ok(responseData);
  }

  // ==================== 私有方法 - 已适配Result模式 ====================

  /**
   * 创建新的引导数据
   * 已适配Result模式：返回XResult类型，增加参数验证
   */
  private async createNewGuideData(characterId: string): Promise<XResult<GuideData>> {
    if (!characterId) {
      return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
    }

    try {
      const guideData: GuideData = {
        uid: characterId,
        nextId: 1, // 从第一个引导开始
        guideFinnishList: new Map(),
        triggerList: new Map(),
        triggerFinishList: new Map(),
        isSkipped: false,
        skipTime: undefined,
      };

      return XResultUtils.ok(guideData);
    } catch (error) {
      return XResultUtils.error(`创建引导数据异常: ${error.message}`, 'CREATE_GUIDE_DATA_ERROR');
    }
  }

  /**
   * 设置引导完成
   * 已适配Result模式：返回XResult类型，增加参数验证
   * 基于old项目setFinish方法
   */
  private async setFinish(guideData: GuideData, guideId: number): Promise<XResult<void>> {
    if (!guideData) {
      return XResultUtils.error('引导数据不能为空', 'INVALID_GUIDE_DATA');
    }

    if (guideId === undefined || guideId === null || guideId < 0) {
      return XResultUtils.error('引导ID无效', 'INVALID_GUIDE_ID');
    }

    try {
      const guideInfo: GuideInfo = {
        id: guideId,
        status: GuideStatus.FINISH,
        finishTime: Date.now(),
      };

      guideData.guideFinnishList.set(guideId, guideInfo);
      return XResultUtils.ok(undefined);
    } catch (error) {
      return XResultUtils.error(`设置引导完成异常: ${error.message}`, 'SET_FINISH_ERROR');
    }
  }

  /**
   * 检查引导是否完成
   * 已适配Result模式：返回XResult类型，增加参数验证
   * 基于old项目checkGuideFinish方法
   */
  private async checkGuideFinish(guideData: GuideData, guideId: number): Promise<XResult<boolean>> {
    if (!guideData) {
      return XResultUtils.error('引导数据不能为空', 'INVALID_GUIDE_DATA');
    }

    if (guideId === undefined || guideId === null || guideId < 0) {
      return XResultUtils.error('引导ID无效', 'INVALID_GUIDE_ID');
    }

    try {
      const guideInfo = guideData.guideFinnishList.get(guideId);
      const isFinished = guideInfo ? guideInfo.status === GuideStatus.FINISH : false;
      return XResultUtils.ok(isFinished);
    } catch (error) {
      return XResultUtils.error(`检查引导完成状态异常: ${error.message}`, 'CHECK_GUIDE_FINISH_ERROR');
    }
  }

  /**
   * 检查是否是新手
   * 已适配Result模式：返回XResult类型，增加参数验证
   * 基于old项目isNewer方法
   */
  private async isNewer(characterId: string): Promise<XResult<boolean>> {
    if (!characterId) {
      return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
    }

    try {
      // TODO: 调用角色服务获取玩家信息，根据等级、注册时间等判断是否是新手
      // const characterResult = await this.callMicroservice(
      //   'character',
      //   'character.getCharacterInfo',
      //   { characterId }
      // );
      //
      // if (XResultUtils.isFailure(characterResult)) {
      //   return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      // }
      //
      // const character = characterResult.data;
      // const isNewer = character.level <= 10 || (Date.now() - character.createTime) < (7 * 24 * 60 * 60 * 1000);
      // return XResultUtils.ok(isNewer);

      // 暂时简化处理，默认为新手
      return XResultUtils.ok(true);
    } catch (error) {
      return XResultUtils.error(`检查新手状态异常: ${error.message}`, 'CHECK_NEWER_ERROR');
    }
  }

  /**
   * 处理引导奖励
   * 已适配Result模式：返回XResult类型，增加参数验证和奖励发放逻辑
   */
  private async processGuideRewards(characterId: string, guideConfig: any): Promise<XResult<any[]>> {
    if (!characterId) {
      return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
    }

    if (!guideConfig) {
      return XResultUtils.error('引导配置不能为空', 'INVALID_GUIDE_CONFIG');
    }

    try {
      const rewards: any[] = [];

      const itemId = guideConfig.Gift;
      const itemNum = guideConfig.Num;

      if (itemId > 0 && itemNum > 0) {
        // TODO: 检查背包空间
        // const spaceCheckResult = await this.callMicroservice(
        //   'inventory',
        //   'inventory.checkSpace',
        //   { characterId, itemId, itemNum }
        // );
        //
        // if (XResultUtils.isFailure(spaceCheckResult)) {
        //   this.logger.warn(`背包空间不足，将通过邮件发放奖励: ${characterId}`);
        //   // 通过邮件发放奖励
        //   const mailResult = await this.callMicroservice(
        //     'mail',
        //     'mail.sendRewardMail',
        //     { characterId, rewards: [{ itemId, itemNum, type: 'item' }], reason: 'guide_reward' }
        //   );
        //
        //   if (XResultUtils.isFailure(mailResult)) {
        //     return XResultUtils.error(`发放引导奖励失败: ${mailResult.message}`, mailResult.code);
        //   }
        // } else {
        //   // 直接添加到背包
        //   const addItemResult = await this.callMicroservice(
        //     'inventory',
        //     'inventory.addItem',
        //     { characterId, itemId, itemNum, reason: 'guide_reward' }
        //   );
        //
        //   if (XResultUtils.isFailure(addItemResult)) {
        //     return XResultUtils.error(`添加物品到背包失败: ${addItemResult.message}`, addItemResult.code);
        //   }
        // }

        rewards.push({
          itemId,
          itemNum,
          type: 'item',
        });

        this.logger.log(`引导奖励处理成功: ${characterId}, 物品ID: ${itemId}, 数量: ${itemNum}`);
      }

      return XResultUtils.ok(rewards);
    } catch (error) {
      return XResultUtils.error(`处理引导奖励异常: ${error.message}`, 'PROCESS_GUIDE_REWARDS_ERROR');
    }
  }

  /**
   * 数据转换方法
   * 已适配Result模式：返回XResult类型，增加参数验证
   * 基于old项目toMap和toArr方法
   */
  private async toMap(arr: any[]): Promise<XResult<Map<number, any>>> {
    try {
      const map = new Map();
      if (!arr || !Array.isArray(arr)) {
        return XResultUtils.ok(map);
      }

      for (const item of arr) {
        if (item && typeof item.id === 'number' && item.guideInfo) {
          map.set(item.id, item.guideInfo);
        }
      }

      return XResultUtils.ok(map);
    } catch (error) {
      return XResultUtils.error(`数组转Map异常: ${error.message}`, 'TO_MAP_ERROR');
    }
  }

  private async toArray(map: Map<number, any>): Promise<XResult<any[]>> {
    try {
      const arr: any[] = [];
      if (!map || !(map instanceof Map)) {
        return XResultUtils.ok(arr);
      }

      for (const [k, v] of map) {
        if (typeof k === 'number' && v) {
          arr.push({
            id: k,
            guideInfo: v,
          });
        }
      }

      return XResultUtils.ok(arr);
    } catch (error) {
      return XResultUtils.error(`Map转数组异常: ${error.message}`, 'TO_ARRAY_ERROR');
    }
  }

  // 数据访问方法 - 已适配Result模式
  /**
   * 获取引导数据
   * 已适配Result模式：返回XResult类型，增加错误处理
   */
  private async getGuideData(characterId: string): Promise<XResult<GuideData | null>> {
    if (!characterId) {
      return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
    }

    try {
      const guideResult = await this.guideRepository.findByCharacterId(characterId);
      if (XResultUtils.isFailure(guideResult)) {
        return XResultUtils.error(`查询引导数据失败: ${guideResult.message}`, guideResult.code);
      }

      const guide = guideResult.data;
      if (!guide) {
        return XResultUtils.ok(null);
      }

      // 转换为业务数据格式
      const guideFinnishList = new Map<number, GuideInfo>();
      if (guide.guideFinnishList && Array.isArray(guide.guideFinnishList)) {
        guide.guideFinnishList.forEach(item => {
          if (item && typeof item.id === 'number') {
            guideFinnishList.set(item.id, {
              id: item.id,
              status: item.status,
              finishTime: item.finishTime,
            });
          }
        });
      }

      const triggerList = new Map<number, TriggerGuideInfo>();
      if (guide.triggerList && Array.isArray(guide.triggerList)) {
        guide.triggerList.forEach(item => {
          if (item && typeof item.id === 'number') {
            triggerList.set(item.id, {
              id: item.id,
              triggerType: item.triggerType,
              param: item.param,
              status: item.status,
              triggerTime: item.triggerTime,
            });
          }
        });
      }

      const triggerFinishList = new Map<number, TriggerGuideInfo>();
      if (guide.triggerFinishList && Array.isArray(guide.triggerFinishList)) {
        guide.triggerFinishList.forEach(item => {
          if (item && typeof item.id === 'number') {
            triggerFinishList.set(item.id, {
              id: item.id,
              triggerType: item.triggerType,
              param: item.param,
              status: item.status,
              triggerTime: item.triggerTime,
            });
          }
        });
      }

      const guideData: GuideData = {
        uid: guide.uid,
        nextId: guide.nextId,
        guideFinnishList,
        triggerList,
        triggerFinishList,
        isSkipped: guide.isSkipped || false,
        skipTime: guide.skipTime,
      };

      return XResultUtils.ok(guideData);
    } catch (error) {
      return XResultUtils.error(`获取引导数据异常: ${error.message}`, 'GET_GUIDE_DATA_ERROR');
    }
  }

  /**
   * 保存引导数据
   * 已适配Result模式：返回XResult类型，增加错误处理
   */
  private async saveGuideData(guideData: GuideData): Promise<XResult<void>> {
    if (!guideData || !guideData.uid) {
      return XResultUtils.error('引导数据或角色ID不能为空', 'INVALID_GUIDE_DATA');
    }

    try {
      // 转换为数据库格式
      const guideFinnishList = Array.from(guideData.guideFinnishList.values());
      const triggerList = Array.from(guideData.triggerList.values());
      const triggerFinishList = Array.from(guideData.triggerFinishList.values());

      // 检查是否存在记录
      const existingGuideResult = await this.guideRepository.findByCharacterId(guideData.uid);
      if (XResultUtils.isFailure(existingGuideResult)) {
        return XResultUtils.error(`查询现有引导记录失败: ${existingGuideResult.message}`, existingGuideResult.code);
      }

      const existingGuide = existingGuideResult.data;

      if (existingGuide) {
        // 更新现有记录
        const updateResult = await this.guideRepository.update(guideData.uid, {
          nextId: guideData.nextId,
          guideFinnishList,
          triggerList,
          triggerFinishList,
          isSkipped: guideData.isSkipped,
          skipTime: guideData.skipTime,
        });

        if (XResultUtils.isFailure(updateResult)) {
          return XResultUtils.error(`更新引导数据失败: ${updateResult.message}`, updateResult.code);
        }
      } else {
        // 创建新记录
        const createResult = await this.guideRepository.create({
          guideRecordId: this.generateGuideRecordId(),
          uid: guideData.uid,
          serverId: 'default', // TODO: 从上下文获取
          nextId: guideData.nextId,
          guideFinnishList,
          triggerList,
          triggerFinishList,
          isSkipped: guideData.isSkipped,
          skipTime: guideData.skipTime,
        });

        if (XResultUtils.isFailure(createResult)) {
          return XResultUtils.error(`创建引导数据失败: ${createResult.message}`, createResult.code);
        }
      }

      return XResultUtils.ok(undefined);
    } catch (error) {
      return XResultUtils.error(`保存引导数据异常: ${error.message}`, 'SAVE_GUIDE_DATA_ERROR');
    }
  }

  /**
   * 获取引导配置列表
   * 已适配Result模式：返回XResult类型，从配置表获取引导配置
   */
  private async getGuideConfigs(): Promise<XResult<any[]>> {
    try {
      // TODO: 从游戏配置表获取引导配置
      // const configResult = await this.gameConfig.guideConfigs.getAll();
      // if (XResultUtils.isFailure(configResult)) {
      //   return XResultUtils.error(`获取引导配置失败: ${configResult.message}`, configResult.code);
      // }
      //
      // return XResultUtils.ok(configResult.data);

      // 暂时返回空数组，等待配置表实现
      return XResultUtils.ok([]);
    } catch (error) {
      return XResultUtils.error(`获取引导配置异常: ${error.message}`, 'GET_GUIDE_CONFIGS_ERROR');
    }
  }

  /**
   * 获取单个引导配置
   * 已适配Result模式：返回XResult类型，从配置表获取指定引导配置
   */
  private async getGuideConfig(guideId: number): Promise<XResult<any | null>> {
    if (guideId === undefined || guideId === null || guideId < 0) {
      return XResultUtils.error('引导ID无效', 'INVALID_GUIDE_ID');
    }

    try {
      // TODO: 从游戏配置表获取单个引导配置
      // const configResult = await this.gameConfig.guideConfigs.get(guideId);
      // if (XResultUtils.isFailure(configResult)) {
      //   return XResultUtils.error(`获取引导配置失败: ${configResult.message}`, configResult.code);
      // }
      //
      // return XResultUtils.ok(configResult.data);

      // 暂时返回null，等待配置表实现
      return XResultUtils.ok(null);
    } catch (error) {
      return XResultUtils.error(`获取引导配置异常: ${error.message}`, 'GET_GUIDE_CONFIG_ERROR');
    }
  }

  /**
   * 生成引导记录ID
   * 已适配Result模式：返回XResult类型，生成唯一的引导记录ID
   */
  private generateGuideRecordId(): string {
    return `guide_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // ==================== 缺失功能补充 ====================

  /**
   * 检查引导触发
   * 已适配Result模式：涉及复杂业务逻辑+多重验证，使用executeBusinessOperation包装
   * 对应old项目中的checkGuideTrigger方法 - 这是引导系统的核心功能
   */
  async checkGuideTrigger(characterId: string, triggerType: GuideTriggerType, param?: any): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId || triggerType === undefined || triggerType === null) {
        return XResultUtils.error('参数不完整：characterId和triggerType不能为空', 'INVALID_PARAMETERS');
      }

      // 1. 初始化引导数据
      const guideDataResult = await this.initGuideData(characterId);
      if (XResultUtils.isFailure(guideDataResult)) {
        return XResultUtils.error(`初始化引导数据失败: ${guideDataResult.message}`, guideDataResult.code);
      }

      const guideData = guideDataResult.data;
      let triggerId = 0;

      // 2. 获取引导配置
      const guideConfigsResult = await this.getGuideConfigs();
      if (XResultUtils.isFailure(guideConfigsResult)) {
        return XResultUtils.error(`获取引导配置失败: ${guideConfigsResult.message}`, guideConfigsResult.code);
      }

      const guideConfigs = guideConfigsResult.data;

      // 3. 检查新手状态
      const isNewerResult = await this.isNewer(characterId);
      const isNewer = XResultUtils.isSuccess(isNewerResult) ? isNewerResult.data : true;

      // 4. 遍历配置查找匹配的触发条件
      for (const config of guideConfigs) {
        const id = config.Id;

        // 老手需要过滤掉新手任务
        const userGroup = config.UserGroup;
        if (userGroup === UserGroupType.NEWER && !isNewer) {
          continue;
        }

        // 检查触发条件
        if (config.Condition === triggerType) {
          // 检查是否已经完成
          if (guideData.guideFinnishList.has(id)) {
            continue;
          }

          // 检查是否已经在触发列表中
          if (guideData.triggerList.has(id)) {
            continue;
          }

          // 检查是否已经在完成的触发列表中
          if (guideData.triggerFinishList.has(id)) {
            continue;
          }

          // 5. 检查参数匹配
          const paramCheckResult = await this.checkTriggerParam(config, param);
          if (XResultUtils.isSuccess(paramCheckResult) && paramCheckResult.data) {
            triggerId = id;

            // 添加到触发列表
            const triggerInfo: TriggerGuideInfo = {
              id,
              triggerType,
              param,
              status: GuideStatus.IN_PROGRESS,
              triggerTime: Date.now(),
            };

            guideData.triggerList.set(id, triggerInfo);

            // 保存数据
            const saveResult = await this.saveGuideData(guideData);
            if (XResultUtils.isFailure(saveResult)) {
              return XResultUtils.error(`保存引导数据失败: ${saveResult.message}`, saveResult.code);
            }

            this.logger.log(`引导触发成功: ${characterId}, 触发类型: ${triggerType}, 引导ID: ${triggerId}`);
            break;
          }
        }
      }

      const responseData = {
        success: triggerId > 0,
        message: triggerId > 0 ? '引导触发成功' : '没有匹配的引导触发条件',
        triggerId,
        triggerType,
        param,
        triggeredAt: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'check_guide_trigger',
      metadata: { characterId, triggerType, param }
    });
  }

  /**
   * 完成触发引导
   * 已适配Result模式：涉及多重验证+奖励处理+数据保存，使用executeBusinessOperation包装
   * 对应old项目中触发引导的完成逻辑
   */
  async completeTriggerGuide(characterId: string, guideId: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId || guideId === undefined || guideId === null) {
        return XResultUtils.error('参数不完整：characterId和guideId不能为空', 'INVALID_PARAMETERS');
      }

      if (guideId < 0) {
        return XResultUtils.error('引导ID无效', 'INVALID_GUIDE_ID');
      }

      // 1. 初始化引导数据
      const guideDataResult = await this.initGuideData(characterId);
      if (XResultUtils.isFailure(guideDataResult)) {
        return XResultUtils.error(`初始化引导数据失败: ${guideDataResult.message}`, guideDataResult.code);
      }

      const guideData = guideDataResult.data;

      // 2. 检查是否在触发列表中
      const triggerInfo = guideData.triggerList.get(guideId);
      if (!triggerInfo) {
        return XResultUtils.failure('引导未触发或已完成', ErrorCode.GUIDE_NOT_TRIGGERED, {
          guideId,
          availableTriggers: Array.from(guideData.triggerList.keys())
        });
      }

      // 3. 移动到完成列表
      triggerInfo.status = GuideStatus.FINISH;
      triggerInfo.finishTime = Date.now();

      guideData.triggerFinishList.set(guideId, triggerInfo);
      guideData.triggerList.delete(guideId);

      // 4. 获取引导配置
      const guideConfigResult = await this.getGuideConfig(guideId);
      if (XResultUtils.isFailure(guideConfigResult)) {
        return XResultUtils.error(`获取引导配置失败: ${guideConfigResult.message}`, guideConfigResult.code);
      }

      const guideConfig = guideConfigResult.data;
      if (!guideConfig) {
        return XResultUtils.error('引导配置不存在', ErrorCode.GUIDE_CONFIG_NOT_FOUND);
      }

      // 5. 处理奖励
      const rewardsResult = await this.processGuideRewards(characterId, guideConfig);
      if (XResultUtils.isFailure(rewardsResult)) {
        this.logger.warn(`处理触发引导奖励失败: ${characterId}, 引导: ${guideId}, ${rewardsResult.message}`);
      }

      const rewards = XResultUtils.isSuccess(rewardsResult) ? rewardsResult.data : [];

      // 6. 保存数据
      const saveResult = await this.saveGuideData(guideData);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存引导数据失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`触发引导完成: ${characterId}, 引导ID: ${guideId}`);

      const responseData = {
        success: true,
        message: '触发引导完成',
        guideId,
        rewards,
        rewardCount: rewards.length,
        completedAt: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'complete_trigger_guide',
      metadata: { characterId, guideId }
    });
  }

  /**
   * 重置引导数据
   * 已适配Result模式：简单的业务操作，不使用executeBusinessOperation
   * 对应old项目中的重置功能
   */
  async resetGuideData(characterId: string): Promise<XResult<any>> {
    // 参数验证
    if (!characterId) {
      return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
    }

    // 初始化引导数据
    const guideDataResult = await this.initGuideData(characterId);
    if (XResultUtils.isFailure(guideDataResult)) {
      return XResultUtils.error(`初始化引导数据失败: ${guideDataResult.message}`, guideDataResult.code);
    }

    const guideData = guideDataResult.data;

    // 重置所有数据
    guideData.guideFinnishList.clear();
    guideData.triggerList.clear();
    guideData.triggerFinishList.clear();
    guideData.nextId = 1; // 重置到第一个引导
    guideData.isSkipped = false;
    guideData.skipTime = undefined;

    // 保存数据
    const saveResult = await this.saveGuideData(guideData);
    if (XResultUtils.isFailure(saveResult)) {
      return XResultUtils.error(`保存引导数据失败: ${saveResult.message}`, saveResult.code);
    }

    this.logger.log(`引导数据重置成功: ${characterId}`);

    const responseData = {
      success: true,
      message: '引导数据已重置',
      resetAt: Date.now(),
    };

    return XResultUtils.ok(responseData);
  }

  /**
   * 设置下一个引导ID
   * 已适配Result模式：简单的业务操作，不使用executeBusinessOperation
   * 对应old项目中的setNextId方法
   */
  async setNextGuideId(characterId: string, nextId: number): Promise<XResult<any>> {
    // 参数验证
    if (!characterId || nextId === undefined || nextId === null) {
      return XResultUtils.error('参数不完整：characterId和nextId不能为空', 'INVALID_PARAMETERS');
    }

    if (nextId < 0) {
      return XResultUtils.error('引导ID不能为负数', 'INVALID_NEXT_ID');
    }

    // 初始化引导数据
    const guideDataResult = await this.initGuideData(characterId);
    if (XResultUtils.isFailure(guideDataResult)) {
      return XResultUtils.error(`初始化引导数据失败: ${guideDataResult.message}`, guideDataResult.code);
    }

    const guideData = guideDataResult.data;
    guideData.nextId = nextId;

    // 保存数据
    const saveResult = await this.saveGuideData(guideData);
    if (XResultUtils.isFailure(saveResult)) {
      return XResultUtils.error(`保存引导数据失败: ${saveResult.message}`, saveResult.code);
    }

    this.logger.log(`设置下一个引导ID成功: ${characterId}, 下一步: ${nextId}`);

    const responseData = {
      success: true,
      message: '下一个引导ID设置成功',
      nextId,
      updatedAt: Date.now(),
    };

    return XResultUtils.ok(responseData);
  }

  // ==================== 私有辅助方法 - 已适配Result模式 ====================

  /**
   * 检查触发参数
   * 已适配Result模式：返回XResult类型，增加参数验证和错误处理
   */
  private async checkTriggerParam(config: any, param?: any): Promise<XResult<boolean>> {
    if (!config) {
      return XResultUtils.error('引导配置不能为空', 'INVALID_CONFIG');
    }

    try {
      // 根据具体的触发类型检查参数
      let isMatch = false;

      switch (config.Condition) {
        case GuideTriggerType.LEVEL_UP:
          // 检查等级参数
          isMatch = !config.ConditionId || (param && typeof param.level === 'number' && param.level >= config.ConditionId);
          break;
        case GuideTriggerType.ENTER_SCENE:
          // 检查场景参数
          isMatch = !config.ConditionId || (param && param.sceneId === config.ConditionId);
          break;
        case GuideTriggerType.COMPLETE_TASK:
          // 检查任务参数
          isMatch = !config.ConditionId || (param && param.taskId === config.ConditionId);
          break;
        case GuideTriggerType.GET_ITEM:
          // 检查物品参数
          isMatch = !config.ConditionId || (param && param.itemId === config.ConditionId);
          break;
        case GuideTriggerType.JOIN_GUILD:
          // 检查公会参数
          isMatch = true; // 加入公会不需要额外参数
          break;
        default:
          isMatch = true; // 默认匹配
          break;
      }

      return XResultUtils.ok(isMatch);
    } catch (error) {
      return XResultUtils.error(`检查触发参数异常: ${error.message}`, 'CHECK_TRIGGER_PARAM_ERROR');
    }
  }
}
