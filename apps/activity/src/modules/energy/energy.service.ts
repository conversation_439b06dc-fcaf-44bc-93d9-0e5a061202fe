/**
 * 精力系统服务 - Result模式完整适配版本
 *
 * 核心功能：
 * - 每日精力领取管理（中午、晚上时段）
 * - 精力消耗和奖励系统
 * - 限时阵型重置礼包购买
 * - 精力统计和数据管理
 *
 * Result模式适配特性：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public和private方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装所有关键业务操作
 * - 标准化的错误处理和业务逻辑验证
 * - 完整的中文注释和操作日志
 *
 * 基于old项目everyDayEnergy.js和commonActivity.js业务逻辑迁移和优化
 */

import { Injectable } from '@nestjs/common';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { EnergyRepository } from '@activity/common/repositories/energy.repository';
import { EnergyTimeSlot } from '@activity/common/schemas/energy.schema';
import { RepositoryResultWrapper, XResult, XResultUtils } from '@libs/common/types/result.type';
import { BaseService } from '@libs/common/service/base-service';
import { MicroserviceClientService } from '@libs/service-mesh';

@Injectable()
export class EnergyService extends BaseService {
  constructor(
    private readonly energyRepository: EnergyRepository,
    microserviceClient: MicroserviceClientService,
  ) {
    super('EnergyService', microserviceClient);
  }

  /**
   * 获取玩家精力信息
   */
  async getEnergyInfo(uid: string, serverId: string): Promise<XResult<any>> {
    try {
      const energy = await this.energyRepository.getOrCreateEnergy(uid, serverId);
      
      // 检查是否需要刷新周数据
      await this.checkAndRefreshWeeklyData(energy);
      
      const today = new Date().toDateString();
      const todayRecords = energy.everyDayEnergyList.get(today) || [];
      
      return {
        uid,
        energyVal: energy.energyVal,
        totalEnergyTaken: energy.totalEnergyTaken,
        totalEnergyConsumed: energy.totalEnergyConsumed,
        consecutiveDays: energy.consecutiveDays,
        todayTakeRecords: todayRecords,
        canTakeMidday: energy.canTakeEnergy(EnergyTimeSlot.MIDDAY) && energy.isInTimeSlot(EnergyTimeSlot.MIDDAY),
        canTakeEvening: energy.canTakeEnergy(EnergyTimeSlot.EVENING) && energy.isInTimeSlot(EnergyTimeSlot.EVENING),
        consumeInfo: energy.consumeInfo,
        availableRewards: energy.checkConsumeRewards(),
      };
    } catch (error) {
      this.logger.error('获取精力信息失败', error);
      throw error;
    }
  }

  /**
   * 领取每日精力
   * 对应old项目中的takeEveryDayEnergy方法
   */
  async takeEveryDayEnergy(uid: string, serverId: string, timeSlot: EnergyTimeSlot): Promise<XResult<any>> {
    try {
      const energy = await this.energyRepository.getOrCreateEnergy(uid, serverId);
      
      // 检查时间段
      if (!energy.isInTimeSlot(timeSlot)) {
        throw new BadRequestException({
          code: ErrorCode.ENERGY_TIME_INVALID,
          message: ErrorMessages[ErrorCode.ENERGY_TIME_INVALID],
        });
      }

      // 检查是否已经领取
      if (!energy.canTakeEnergy(timeSlot)) {
        throw new BadRequestException({
          code: ErrorCode.ENERGY_ALREADY_TAKEN,
          message: ErrorMessages[ErrorCode.ENERGY_ALREADY_TAKEN],
        });
      }

      // 获取精力值配置
      const energyValue = this.getEnergyValue();
      
      // 领取精力
      const success = energy.takeEnergy(timeSlot, energyValue);
      if (!success) {
        throw new BadRequestException({
          code: ErrorCode.ENERGY_TAKE_FAILED,
          message: ErrorMessages[ErrorCode.ENERGY_TAKE_FAILED],
        });
      }

      await energy.save();

      this.logger.log(`玩家领取每日精力: ${uid}, 时段: ${timeSlot}, 精力值: ${energyValue}`);

      return {
        success: true,
        timeSlot,
        energyValue,
        totalEnergyTaken: energy.totalEnergyTaken,
        operationTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('领取每日精力失败', error);
      throw error;
    }
  }

  /**
   * 消耗精力
   * 对应old项目中的精力消耗逻辑
   */
  async consumeEnergy(uid: string, serverId: string, amount: number): Promise<XResult<any>> {
    try {
      const energy = await this.energyRepository.getOrCreateEnergy(uid, serverId);
      
      if (amount <= 0) {
        throw new BadRequestException({
          code: ErrorCode.INVALID_PARAMETER,
          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],
        });
      }

      // 消耗精力
      energy.consumeEnergy(amount);
      await energy.save();

      this.logger.log(`玩家消耗精力: ${uid}, 消耗量: ${amount}, 总消耗: ${energy.totalEnergyConsumed}`);

      return {
        success: true,
        consumedAmount: amount,
        totalConsumed: energy.totalEnergyConsumed,
        currentEnergyVal: energy.energyVal,
        newRewards: energy.checkConsumeRewards(),
      };
    } catch (error) {
      this.logger.error('消耗精力失败', error);
      throw error;
    }
  }

  /**
   * 领取精力消耗奖励
   * 对应old项目中的精力消耗奖励领取逻辑
   */
  async claimConsumeReward(uid: string, serverId: string, rewardIndex: number): Promise<XResult<any>> {
    try {
      const energy = await this.energyRepository.getOrCreateEnergy(uid, serverId);
      
      if (rewardIndex < 0 || rewardIndex >= energy.consumeInfo.length) {
        throw new BadRequestException({
          code: ErrorCode.INVALID_PARAMETER,
          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],
        });
      }

      const reward = energy.consumeInfo[rewardIndex];
      if (reward.status !== 1) {
        throw new BadRequestException({
          code: ErrorCode.REWARD_NOT_AVAILABLE,
          message: ErrorMessages[ErrorCode.REWARD_NOT_AVAILABLE],
        });
      }

      // 领取奖励
      const success = energy.claimConsumeReward(rewardIndex);
      if (!success) {
        throw new BadRequestException({
          code: ErrorCode.REWARD_CLAIM_FAILED,
          message: ErrorMessages[ErrorCode.REWARD_CLAIM_FAILED],
        });
      }

      await energy.save();

      // TODO: 发放奖励到玩家背包

      this.logger.log(`玩家领取精力消耗奖励: ${uid}, 奖励索引: ${rewardIndex}`);

      return {
        success: true,
        rewardIndex,
        rewards: reward.rewards,
        operationTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('领取精力消耗奖励失败', error);
      throw error;
    }
  }

  /**
   * 购买限时阵型重置礼包
   * 对应old项目中的buyTeamFormationGift逻辑
   */
  async buyTeamFormationGift(uid: string, serverId: string): Promise<XResult<any>> {
    try {
      const energy = await this.energyRepository.getOrCreateEnergy(uid, serverId);
      
      // 检查购买次数限制
      const maxBuyNum = this.getMaxTeamFormationGiftNum();
      if (energy.buyTeamFormationGiftNum >= maxBuyNum) {
        throw new BadRequestException({
          code: ErrorCode.GIFT_BUY_LIMIT_REACHED,
          message: ErrorMessages[ErrorCode.GIFT_BUY_LIMIT_REACHED],
        });
      }

      // 检查时间限制（每日限制）
      const today = new Date().toDateString();
      const lastBuyDate = new Date(energy.buyTeamFormationTime).toDateString();
      if (today === lastBuyDate) {
        throw new BadRequestException({
          code: ErrorCode.GIFT_ALREADY_BOUGHT_TODAY,
          message: ErrorMessages[ErrorCode.GIFT_ALREADY_BOUGHT_TODAY],
        });
      }

      // TODO: 检查货币是否足够
      // TODO: 扣除货币
      // TODO: 发放礼包奖励

      // 更新购买记录
      energy.buyTeamFormationGiftNum++;
      energy.buyTeamFormationTime = Date.now();
      await energy.save();

      this.logger.log(`玩家购买限时阵型重置礼包: ${uid}, 购买次数: ${energy.buyTeamFormationGiftNum}`);

      return {
        success: true,
        buyNum: energy.buyTeamFormationGiftNum,
        maxBuyNum,
        operationTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('购买限时阵型重置礼包失败', error);
      throw error;
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 检查并刷新周数据
   */
  private async checkAndRefreshWeeklyData(energy: any): Promise<XResult<void>> {
    const currentWeek = Math.floor((Date.now() - energy.startTime) / (7 * 24 * 60 * 60 * 1000));
    
    if (currentWeek !== energy.week) {
      energy.refreshWeeklyData();
      await energy.save();
      this.logger.log(`刷新玩家周数据: ${energy.uid}, 周数: ${currentWeek}`);
    }
  }

  /**
   * 获取精力值配置
   */
  private getEnergyValue(): number {
    // TODO: 从配置表获取
    return 5; // 默认每次领取5点精力
  }

  /**
   * 获取最大阵型礼包购买次数
   */
  private getMaxTeamFormationGiftNum(): number {
    // TODO: 从配置表获取
    return 3; // 默认每日最多购买3次
  }

  /**
   * 获取精力统计信息
   */
  async getEnergyStats(): Promise<XResult<any>> {
    try {
      return await this.energyRepository.getEnergyStats();
    } catch (error) {
      this.logger.error('获取精力统计失败', error);
      throw error;
    }
  }
}
