/**
 * 精力系统服务 - Result模式完整适配版本
 *
 * 核心功能：
 * - 每日精力领取管理（中午、晚上时段）
 * - 精力消耗和奖励系统
 * - 限时阵型重置礼包购买
 * - 精力统计和数据管理
 *
 * Result模式适配特性：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public和private方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装所有关键业务操作
 * - 标准化的错误处理和业务逻辑验证
 * - 完整的中文注释和操作日志
 *
 * 基于old项目everyDayEnergy.js和commonActivity.js业务逻辑迁移和优化
 */

import { Injectable } from '@nestjs/common';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { EnergyRepository } from '@activity/common/repositories/energy.repository';
import { EnergyTimeSlot } from '@activity/common/schemas/energy.schema';
import { RepositoryResultWrapper, XResult, XResultUtils } from '@libs/common/types/result.type';
import { BaseService } from '@libs/common/service/base-service';
import { MicroserviceClientService } from '@libs/service-mesh';

@Injectable()
export class EnergyService extends BaseService {
  constructor(
    private readonly energyRepository: EnergyRepository,
    microserviceClient: MicroserviceClientService,
  ) {
    super('EnergyService', microserviceClient);
  }

  /**
   * 获取玩家精力信息
   * 已适配Result模式：涉及多步骤业务流程（获取数据+刷新周数据），使用executeBusinessOperation包装
   */
  async getEnergyInfo(uid: string, serverId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!uid || !serverId) {
        return XResultUtils.error('参数不完整：uid和serverId不能为空', 'INVALID_PARAMETERS');
      }

      // 1. 获取精力数据
      const energyResult = await this.energyRepository.getOrCreateEnergy(uid, serverId);
      if (XResultUtils.isFailure(energyResult)) {
        return XResultUtils.error(`获取精力数据失败: ${energyResult.message}`, energyResult.code);
      }

      const energy = energyResult.data;
      if (!energy) {
        return XResultUtils.error('精力数据不存在', 'ENERGY_DATA_NOT_FOUND');
      }

      // 2. 检查并刷新周数据（多步骤业务逻辑）
      const refreshResult = await this.checkAndRefreshWeeklyData(energy);
      if (XResultUtils.isFailure(refreshResult)) {
        this.logger.warn(`刷新周数据失败: ${refreshResult.message}`);
      }

      const today = new Date().toDateString();
      const todayRecords = energy.everyDayEnergyList.get(today) || [];

      const responseData = {
        uid,
        energyVal: energy.energyVal,
        totalEnergyTaken: energy.totalEnergyTaken,
        totalEnergyConsumed: energy.totalEnergyConsumed,
        consecutiveDays: energy.consecutiveDays,
        todayTakeRecords: todayRecords,
        canTakeMidday: energy.canTakeEnergy(EnergyTimeSlot.MIDDAY) && energy.isInTimeSlot(EnergyTimeSlot.MIDDAY),
        canTakeEvening: energy.canTakeEnergy(EnergyTimeSlot.EVENING) && energy.isInTimeSlot(EnergyTimeSlot.EVENING),
        consumeInfo: energy.consumeInfo,
        availableRewards: energy.checkConsumeRewards(),
        lastUpdateTime: energy.lastUpdateTime || Date.now(),
      };

      this.logger.log(`获取精力信息成功: ${uid}, 当前精力: ${energy.energyVal}, 可领取奖励: ${energy.checkConsumeRewards().length}个`);

      return XResultUtils.ok(responseData);
    }, {
      reason: 'get_energy_info',
      metadata: { uid, serverId }
    });
  }

  /**
   * 领取每日精力
   * 已适配Result模式：涉及多重验证+业务操作+数据保存，使用executeBusinessOperation包装
   * 对应old项目中的takeEveryDayEnergy方法
   */
  async takeEveryDayEnergy(uid: string, serverId: string, timeSlot: EnergyTimeSlot): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!uid || !serverId || timeSlot === undefined || timeSlot === null) {
        return XResultUtils.error('参数不完整：uid、serverId和timeSlot不能为空', 'INVALID_PARAMETERS');
      }

      // 1. 获取精力数据
      const energyResult = await this.energyRepository.getOrCreateEnergy(uid, serverId);
      if (XResultUtils.isFailure(energyResult)) {
        return XResultUtils.error(`获取精力数据失败: ${energyResult.message}`, energyResult.code);
      }

      const energy = energyResult.data;
      if (!energy) {
        return XResultUtils.error('精力数据不存在', 'ENERGY_DATA_NOT_FOUND');
      }

      // 2. 业务规则验证
      if (!energy.isInTimeSlot(timeSlot)) {
        return XResultUtils.failure('不在精力领取时间段内', ErrorCode.ENERGY_TIME_INVALID, {
          timeSlot,
          currentTime: new Date().toISOString()
        });
      }

      if (!energy.canTakeEnergy(timeSlot)) {
        return XResultUtils.failure('该时段精力已领取', ErrorCode.ENERGY_ALREADY_TAKEN, {
          timeSlot,
          todayRecords: energy.everyDayEnergyList.get(new Date().toDateString()) || []
        });
      }

      // 3. 获取精力值配置
      const energyValueResult = await this.getEnergyValue();
      if (XResultUtils.isFailure(energyValueResult)) {
        return XResultUtils.error(`获取精力配置失败: ${energyValueResult.message}`, energyValueResult.code);
      }

      const energyValue = energyValueResult.data;

      // 4. 执行领取操作
      const success = energy.takeEnergy(timeSlot, energyValue);
      if (!success) {
        return XResultUtils.error('精力领取操作失败', ErrorCode.ENERGY_TAKE_FAILED);
      }

      // 5. 保存数据
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await energy.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存精力数据失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`玩家领取每日精力成功: ${uid}, 时段: ${timeSlot}, 精力值: ${energyValue}`);

      const responseData = {
        success: true,
        message: '精力领取成功',
        timeSlot,
        energyValue,
        totalEnergyTaken: energy.totalEnergyTaken,
        currentEnergyVal: energy.energyVal,
        operationTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'take_every_day_energy',
      metadata: { uid, serverId, timeSlot }
    });
  }

  /**
   * 消耗精力
   * 已适配Result模式：简单的业务操作，不使用executeBusinessOperation
   * 对应old项目中的精力消耗逻辑
   */
  async consumeEnergy(uid: string, serverId: string, amount: number): Promise<XResult<any>> {
    // 参数验证
    if (!uid || !serverId || amount === undefined || amount === null) {
      return XResultUtils.error('参数不完整：uid、serverId和amount不能为空', 'INVALID_PARAMETERS');
    }

    if (amount <= 0) {
      return XResultUtils.error('消耗精力数量必须大于0', 'INVALID_AMOUNT');
    }

    // 获取精力数据
    const energyResult = await this.energyRepository.getOrCreateEnergy(uid, serverId);
    if (XResultUtils.isFailure(energyResult)) {
      return XResultUtils.error(`获取精力数据失败: ${energyResult.message}`, energyResult.code);
    }

    const energy = energyResult.data;
    if (!energy) {
      return XResultUtils.error('精力数据不存在', 'ENERGY_DATA_NOT_FOUND');
    }

    // 检查精力是否足够
    if (energy.energyVal < amount) {
      return XResultUtils.failure('精力不足', 'INSUFFICIENT_ENERGY', {
        required: amount,
        current: energy.energyVal,
        deficit: amount - energy.energyVal
      });
    }

    // 消耗精力
    energy.consumeEnergy(amount);

    // 保存数据
    const saveResult = await RepositoryResultWrapper.wrap(async () => {
      return await energy.save();
    });
    if (XResultUtils.isFailure(saveResult)) {
      return XResultUtils.error(`保存精力数据失败: ${saveResult.message}`, saveResult.code);
    }

    this.logger.log(`玩家消耗精力成功: ${uid}, 消耗量: ${amount}, 总消耗: ${energy.totalEnergyConsumed}`);

    const responseData = {
      success: true,
      message: '精力消耗成功',
      consumedAmount: amount,
      totalConsumed: energy.totalEnergyConsumed,
      currentEnergyVal: energy.energyVal,
      newRewards: energy.checkConsumeRewards(),
      operationTime: Date.now(),
    };

    return XResultUtils.ok(responseData);
  }

  /**
   * 领取精力消耗奖励
   * 已适配Result模式：涉及多重验证+奖励发放+数据保存，使用executeBusinessOperation包装
   * 对应old项目中的精力消耗奖励领取逻辑
   */
  async claimConsumeReward(uid: string, serverId: string, rewardIndex: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!uid || !serverId || rewardIndex === undefined || rewardIndex === null) {
        return XResultUtils.error('参数不完整：uid、serverId和rewardIndex不能为空', 'INVALID_PARAMETERS');
      }

      // 1. 获取精力数据
      const energyResult = await this.energyRepository.getOrCreateEnergy(uid, serverId);
      if (XResultUtils.isFailure(energyResult)) {
        return XResultUtils.error(`获取精力数据失败: ${energyResult.message}`, energyResult.code);
      }

      const energy = energyResult.data;
      if (!energy) {
        return XResultUtils.error('精力数据不存在', 'ENERGY_DATA_NOT_FOUND');
      }

      // 2. 验证奖励索引
      if (rewardIndex < 0 || rewardIndex >= energy.consumeInfo.length) {
        return XResultUtils.error('奖励索引无效', 'INVALID_REWARD_INDEX');
      }

      const reward = energy.consumeInfo[rewardIndex];
      if (reward.status !== 1) {
        return XResultUtils.failure('奖励不可领取', 'REWARD_NOT_AVAILABLE', {
          rewardIndex,
          currentStatus: reward.status,
          requiredStatus: 1
        });
      }

      // 3. 领取奖励
      const success = energy.claimConsumeReward(rewardIndex);
      if (!success) {
        return XResultUtils.error('奖励领取操作失败', 'REWARD_CLAIM_FAILED');
      }

      // 4. 保存数据
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await energy.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存精力数据失败: ${saveResult.message}`, saveResult.code);
      }

      // 5. 发放奖励到玩家背包（调用微服务）
      const rewardDistributionResult = await this.distributeRewards(uid, serverId, reward.rewards);
      if (XResultUtils.isFailure(rewardDistributionResult)) {
        this.logger.warn(`发放奖励失败: ${uid}, 奖励索引: ${rewardIndex}, ${rewardDistributionResult.message}`);
        // 奖励发放失败不影响领取状态，但需要记录日志
      }

      this.logger.log(`玩家领取精力消耗奖励成功: ${uid}, 奖励索引: ${rewardIndex}`);

      const responseData = {
        success: true,
        message: '精力消耗奖励领取成功',
        rewardIndex,
        rewards: reward.rewards,
        rewardDistributed: XResultUtils.isSuccess(rewardDistributionResult),
        operationTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'claim_consume_reward',
      metadata: { uid, serverId, rewardIndex }
    });
  }

  /**
   * 购买限时阵型重置礼包
   * 已适配Result模式：涉及多重验证+微服务调用+数据保存，使用executeBusinessOperation包装
   * 对应old项目中的buyTeamFormationGift逻辑
   */
  async buyTeamFormationGift(uid: string, serverId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!uid || !serverId) {
        return XResultUtils.error('参数不完整：uid和serverId不能为空', 'INVALID_PARAMETERS');
      }

      // 1. 获取精力数据
      const energyResult = await this.energyRepository.getOrCreateEnergy(uid, serverId);
      if (XResultUtils.isFailure(energyResult)) {
        return XResultUtils.error(`获取精力数据失败: ${energyResult.message}`, energyResult.code);
      }

      const energy = energyResult.data;
      if (!energy) {
        return XResultUtils.error('精力数据不存在', 'ENERGY_DATA_NOT_FOUND');
      }

      // 2. 获取购买限制配置
      const maxBuyNumResult = await this.getMaxTeamFormationGiftNum();
      if (XResultUtils.isFailure(maxBuyNumResult)) {
        return XResultUtils.error(`获取购买限制配置失败: ${maxBuyNumResult.message}`, maxBuyNumResult.code);
      }

      const maxBuyNum = maxBuyNumResult.data;

      // 3. 验证购买次数限制
      if (energy.buyTeamFormationGiftNum >= maxBuyNum) {
        return XResultUtils.failure('购买次数已达上限', 'GIFT_BUY_LIMIT_REACHED', {
          currentBuyNum: energy.buyTeamFormationGiftNum,
          maxBuyNum
        });
      }

      // 4. 验证时间限制（每日限制）
      const today = new Date().toDateString();
      const lastBuyDate = new Date(energy.buyTeamFormationTime).toDateString();
      if (today === lastBuyDate) {
        return XResultUtils.failure('今日已购买过该礼包', 'GIFT_ALREADY_BOUGHT_TODAY', {
          lastBuyTime: energy.buyTeamFormationTime,
          todayDate: today
        });
      }

      // 5. 检查货币是否足够（调用微服务）
      const costCheckResult = await this.checkGiftCost(uid, serverId);
      if (XResultUtils.isFailure(costCheckResult)) {
        return XResultUtils.error(`检查购买费用失败: ${costCheckResult.message}`, costCheckResult.code);
      }

      // 6. 扣除货币（调用微服务）
      const deductResult = await this.deductGiftCost(uid, serverId, costCheckResult.data);
      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除费用失败: ${deductResult.message}`, deductResult.code);
      }

      // 7. 发放礼包奖励（调用微服务）
      const rewardResult = await this.distributeGiftRewards(uid, serverId);
      if (XResultUtils.isFailure(rewardResult)) {
        this.logger.warn(`发放礼包奖励失败: ${uid}, ${rewardResult.message}`);
        // 奖励发放失败不影响购买状态，但需要记录日志
      }

      // 8. 更新购买记录
      energy.buyTeamFormationGiftNum++;
      energy.buyTeamFormationTime = Date.now();

      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await energy.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存购买记录失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`玩家购买限时阵型重置礼包成功: ${uid}, 购买次数: ${energy.buyTeamFormationGiftNum}`);

      const responseData = {
        success: true,
        message: '限时阵型重置礼包购买成功',
        buyNum: energy.buyTeamFormationGiftNum,
        maxBuyNum,
        cost: costCheckResult.data,
        rewardDistributed: XResultUtils.isSuccess(rewardResult),
        operationTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'buy_team_formation_gift',
      metadata: { uid, serverId }
    });
  }

  // ==================== 私有方法 - 已适配Result模式 ====================

  /**
   * 检查并刷新周数据
   * 已适配Result模式：返回XResult类型，处理数据刷新逻辑
   */
  private async checkAndRefreshWeeklyData(energy: any): Promise<XResult<void>> {
    try {
      const currentWeek = Math.floor((Date.now() - energy.startTime) / (7 * 24 * 60 * 60 * 1000));

      if (currentWeek !== energy.week) {
        energy.refreshWeeklyData();

        const saveResult = await RepositoryResultWrapper.wrap(async () => {
          return await energy.save();
        });
        if (XResultUtils.isFailure(saveResult)) {
          return XResultUtils.error(`保存周数据刷新失败: ${saveResult.message}`, saveResult.code);
        }

        this.logger.log(`刷新玩家周数据成功: ${energy.uid}, 周数: ${currentWeek}`);
      }

      return XResultUtils.ok(undefined);
    } catch (error) {
      return XResultUtils.error(`检查周数据异常: ${error.message}`, 'CHECK_WEEKLY_DATA_ERROR');
    }
  }

  /**
   * 获取精力值配置
   * 已适配Result模式：返回XResult类型，从配置获取精力值
   */
  private async getEnergyValue(): Promise<XResult<number>> {
    // TODO: 从配置表获取
    // const configResult = await this.gameConfig.energySettings.get('dailyEnergyValue');
    // if (XResultUtils.isSuccess(configResult)) {
    //   return XResultUtils.ok(configResult.data);
    // }

    // 默认每次领取5点精力
    const defaultEnergyValue = 5;
    return XResultUtils.ok(defaultEnergyValue);
  }

  /**
   * 获取最大阵型礼包购买次数
   * 已适配Result模式：返回XResult类型，从配置获取购买限制
   */
  private async getMaxTeamFormationGiftNum(): Promise<XResult<number>> {
    // TODO: 从配置表获取
    // const configResult = await this.gameConfig.giftSettings.get('maxTeamFormationGiftNum');
    // if (XResultUtils.isSuccess(configResult)) {
    //   return XResultUtils.ok(configResult.data);
    // }

    // 默认每日最多购买3次
    const defaultMaxBuyNum = 3;
    return XResultUtils.ok(defaultMaxBuyNum);
  }

  /**
   * 发放奖励到玩家背包
   * 已适配Result模式：返回XResult类型，调用微服务发放奖励
   */
  private async distributeRewards(uid: string, serverId: string, rewards: any[]): Promise<XResult<void>> {
    // TODO: 调用背包服务发放奖励
    // const result = await this.callMicroservice(
    //   'inventory',
    //   'inventory.addItems',
    //   { uid, serverId, items: rewards }
    // );
    //
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`发放奖励失败: ${result.message}`, result.code);
    // }

    // 暂时返回成功，等待实际微服务调用实现
    return XResultUtils.ok(undefined);
  }

  /**
   * 检查礼包购买费用
   * 已适配Result模式：返回XResult类型，调用微服务检查费用
   */
  private async checkGiftCost(uid: string, serverId: string): Promise<XResult<any>> {
    // TODO: 调用角色服务检查货币
    // const result = await this.callMicroservice(
    //   'character',
    //   'character.checkCurrency',
    //   { uid, serverId, currencyType: 'gold', amount: 100 }
    // );
    //
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`检查费用失败: ${result.message}`, result.code);
    // }

    // 暂时返回模拟费用数据
    const mockCost = { currencyType: 'gold', amount: 100 };
    return XResultUtils.ok(mockCost);
  }

  /**
   * 扣除礼包购买费用
   * 已适配Result模式：返回XResult类型，调用微服务扣除费用
   */
  private async deductGiftCost(uid: string, serverId: string, cost: any): Promise<XResult<void>> {
    // TODO: 调用角色服务扣除货币
    // const result = await this.callMicroservice(
    //   'character',
    //   'character.deductCurrency',
    //   { uid, serverId, currencyType: cost.currencyType, amount: cost.amount, reason: 'buy_team_formation_gift' }
    // );
    //
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`扣除费用失败: ${result.message}`, result.code);
    // }

    // 暂时返回成功，等待实际微服务调用实现
    return XResultUtils.ok(undefined);
  }

  /**
   * 发放礼包奖励
   * 已适配Result模式：返回XResult类型，调用微服务发放礼包奖励
   */
  private async distributeGiftRewards(uid: string, serverId: string): Promise<XResult<void>> {
    // TODO: 调用背包服务发放礼包奖励
    // const giftRewards = [
    //   { type: 'item', itemId: 'formation_reset_card', amount: 1 },
    //   { type: 'gold', amount: 500 }
    // ];
    //
    // const result = await this.callMicroservice(
    //   'inventory',
    //   'inventory.addItems',
    //   { uid, serverId, items: giftRewards }
    // );
    //
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`发放礼包奖励失败: ${result.message}`, result.code);
    // }

    // 暂时返回成功，等待实际微服务调用实现
    return XResultUtils.ok(undefined);
  }

  /**
   * 获取精力统计信息
   * 已适配Result模式：简单的Repository调用，直接返回结果
   */
  async getEnergyStats(): Promise<XResult<any>> {
    const statsResult = await this.energyRepository.getEnergyStats();
    if (XResultUtils.isFailure(statsResult)) {
      return XResultUtils.error(`获取精力统计失败: ${statsResult.message}`, statsResult.code);
    }

    return XResultUtils.ok(statsResult.data);
  }
}
