/**
 * 活动事件服务 - Result模式完整适配版本
 *
 * 核心功能：
 * - 活动数据管理和状态跟踪
 * - 活动进度更新和奖励发放
 * - 抽奖系统（最佳11人、老虎机、拉霸等）
 * - 首充和开服礼包管理
 *
 * Result模式适配特性：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public和private方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装复杂业务操作
 * - 标准化的错误处理和业务逻辑验证
 * - 完整的中文注释和操作日志
 *
 * 基于old项目Act业务逻辑迁移和优化
 */

import { Injectable } from '@nestjs/common';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { EventRepository } from '@activity/common/repositories/event.repository';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@libs/shared/constants';
import { GameConfigFacade } from '@libs/game-config';
import { RepositoryResultWrapper, XResult, XResultUtils } from '@libs/common/types/result.type';
import { BaseService } from '@libs/common/service/base-service';

import {
  ActivityTimeType,
  ActivityRefreshType,
  ActivityType,
  FirstChargeStatus
} from '@activity/common/schemas/event.schema';

// 活动记录接口（基于old项目actRecord结构）
export interface ActivityRecord {
  actId: number;       // 活动ID
  actType: ActivityType; // 活动类型
  periods: number;     // 期数
  progress: number;    // 进度
  status: number;      // 状态
  rewards: any[];      // 已领取奖励
  data: any;           // 活动特定数据
  createTime: number;  // 创建时间
  updateTime: number;  // 更新时间
}

// 活动信息接口（基于old项目globalCurrActList结构）
export interface ActivityInfo {
  actId: number;       // 活动ID
  actType: ActivityType; // 活动类型
  actName: string;     // 活动名称
  startTime: number;   // 开始时间
  endTime: number;     // 结束时间
  timeType: ActivityTimeType; // 时间类型
  refreshCycle: ActivityRefreshType; // 刷新周期
  periods: number;     // 期数
  isActive: boolean;   // 是否激活
  config: any;         // 配置数据
}

// 活动数据接口（基于old项目Act结构）
export interface ActivityData {
  uid: string;         // 玩家UID
  globalCurrActList: Map<number, ActivityInfo>; // 当前活动列表
  globalActMgrInfo: Map<number, ActivityRecord>; // 活动管理信息
  historyActList: Map<number, any>; // 历史活动数据
  firstChargeStatus: FirstChargeStatus; // 首充状态
  openChargeStatus: FirstChargeStatus; // 开服礼包状态
}

@Injectable()
export class EventService extends BaseService {
  constructor(
    private readonly eventRepository: EventRepository,
    private readonly gameConfig: GameConfigFacade,
    microserviceClient: MicroserviceClientService,
  ) {
    super('EventService', microserviceClient);
  }

  /**
   * 初始化活动数据
   * 已适配Result模式：涉及多步骤业务流程，使用executeBusinessOperation包装
   * 基于old项目initByDB方法
   */
  async initActivityData(characterId: string): Promise<XResult<ActivityData>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId) {
        return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
      }

      // 1. 从数据库加载活动数据
      const activityDataResult = await this.getActivityData(characterId);
      if (XResultUtils.isFailure(activityDataResult)) {
        return XResultUtils.error(`获取活动数据失败: ${activityDataResult.message}`, activityDataResult.code);
      }

      let activityData = activityDataResult.data;
      if (!activityData) {
        // 创建新的活动数据
        const newActivityDataResult = await this.createNewActivityData(characterId);
        if (XResultUtils.isFailure(newActivityDataResult)) {
          return XResultUtils.error(`创建活动数据失败: ${newActivityDataResult.message}`, newActivityDataResult.code);
        }
        activityData = newActivityDataResult.data;
      }

      // 2. 加载全局活动（基于old项目loadGlobalAct方法）
      const loadResult = await this.loadGlobalActivities(activityData);
      if (XResultUtils.isFailure(loadResult)) {
        return XResultUtils.error(`加载全局活动失败: ${loadResult.message}`, loadResult.code);
      }

      return XResultUtils.ok(activityData);
    }, {
      reason: 'init_activity_data',
      metadata: { characterId }
    });
  }

  /**
   * 获取活动列表
   * 已适配Result模式：涉及多步骤业务流程，使用executeBusinessOperation包装
   * 基于old项目getActList方法
   */
  async getActivityList(characterId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId) {
        return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
      }

      // 1. 初始化活动数据
      const activityDataResult = await this.initActivityData(characterId);
      if (XResultUtils.isFailure(activityDataResult)) {
        return XResultUtils.error(`初始化活动数据失败: ${activityDataResult.message}`, activityDataResult.code);
      }

      const activityData = activityDataResult.data;

      // 2. 检查并重新加载活动控制
      const reloadResult = await this.checkAndReloadActivityControl(activityData);
      if (XResultUtils.isFailure(reloadResult)) {
        this.logger.warn(`重新加载活动控制失败: ${reloadResult.message}`);
      }

      // 3. 构建活动列表
      const activeActivities: any[] = [];
      const now = Date.now();

      for (const [actId, actInfo] of activityData.globalCurrActList) {
        // 检查活动是否在有效期内
        if (now >= actInfo.startTime && now <= actInfo.endTime) {
          const actRecord = activityData.globalActMgrInfo.get(actId);

          activeActivities.push({
            actId: actInfo.actId,
            actType: actInfo.actType,
            actName: actInfo.actName,
            startTime: actInfo.startTime,
            endTime: actInfo.endTime,
            timeType: actInfo.timeType,
            periods: actInfo.periods,
            progress: actRecord?.progress || 0,
            status: actRecord?.status || 0,
            isActive: actInfo.isActive,
            remainingTime: actInfo.endTime - now,
          });
        }
      }

      this.logger.log(`获取活动列表成功: ${characterId}, 活动数量: ${activeActivities.length}`);

      const responseData = {
        success: true,
        message: '获取活动列表成功',
        activities: activeActivities,
        total: activeActivities.length,
        activeCount: activeActivities.filter(act => act.isActive).length,
        firstChargeStatus: activityData.firstChargeStatus,
        openChargeStatus: activityData.openChargeStatus,
        operationTime: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'get_activity_list',
      metadata: { characterId }
    });
  }

  /**
   * 获取活动详情
   * 已适配Result模式：简单的业务操作，不使用executeBusinessOperation
   * 基于old项目getActInfo方法
   */
  async getActivityInfo(characterId: string, actId: number): Promise<XResult<any>> {
    // 参数验证
    if (!characterId || actId === undefined || actId === null) {
      return XResultUtils.error('参数不完整：characterId和actId不能为空', 'INVALID_PARAMETERS');
    }

    if (actId < 0) {
      return XResultUtils.error('活动ID无效', 'INVALID_ACTIVITY_ID');
    }

    // 初始化活动数据
    const activityDataResult = await this.initActivityData(characterId);
    if (XResultUtils.isFailure(activityDataResult)) {
      return XResultUtils.error(`初始化活动数据失败: ${activityDataResult.message}`, activityDataResult.code);
    }

    const activityData = activityDataResult.data;

    // 查找活动信息
    const actInfo = activityData.globalCurrActList.get(actId);
    if (!actInfo) {
      return XResultUtils.error('活动不存在', ErrorCode.ACTIVITY_NOT_FOUND);
    }

    const actRecord = activityData.globalActMgrInfo.get(actId);
    const now = Date.now();

    // 检查活动是否在有效期内
    if (now < actInfo.startTime || now > actInfo.endTime) {
      return XResultUtils.failure('活动未开始或已结束', 'ACTIVITY_NOT_ACTIVE', {
        actId,
        startTime: actInfo.startTime,
        endTime: actInfo.endTime,
        currentTime: now
      });
    }

    const responseData = {
      success: true,
      message: '获取活动详情成功',
      activity: {
        actId: actInfo.actId,
        actType: actInfo.actType,
        actName: actInfo.actName,
        startTime: actInfo.startTime,
        endTime: actInfo.endTime,
        progress: actRecord?.progress || 0,
        status: actRecord?.status || 0,
        rewards: actRecord?.rewards || [],
        data: actRecord?.data || {},
        config: actInfo.config,
        remainingTime: actInfo.endTime - now,
      },
    };

    return XResultUtils.ok(responseData);
  }

  /**
   * 更新活动进度
   * 已适配Result模式：涉及多重验证+数据更新+保存，使用executeBusinessOperation包装
   * 基于old项目updateActProgress方法
   */
  async updateActivityProgress(characterId: string, actId: number, progress: number, data?: any): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId || actId === undefined || actId === null || progress === undefined || progress === null) {
        return XResultUtils.error('参数不完整：characterId、actId和progress不能为空', 'INVALID_PARAMETERS');
      }

      if (actId < 0) {
        return XResultUtils.error('活动ID无效', 'INVALID_ACTIVITY_ID');
      }

      if (progress < 0) {
        return XResultUtils.error('进度值不能为负数', 'INVALID_PROGRESS_VALUE');
      }

      // 1. 初始化活动数据
      const activityDataResult = await this.initActivityData(characterId);
      if (XResultUtils.isFailure(activityDataResult)) {
        return XResultUtils.error(`初始化活动数据失败: ${activityDataResult.message}`, activityDataResult.code);
      }

      const activityData = activityDataResult.data;

      // 2. 验证活动存在
      const actInfo = activityData.globalCurrActList.get(actId);
      if (!actInfo) {
        return XResultUtils.error('活动不存在', ErrorCode.ACTIVITY_NOT_FOUND);
      }

      // 3. 获取或创建活动记录
      let actRecord = activityData.globalActMgrInfo.get(actId);
      if (!actRecord) {
        const createRecordResult = await this.createActivityRecord(actId, actInfo.actType, actInfo.periods);
        if (XResultUtils.isFailure(createRecordResult)) {
          return XResultUtils.error(`创建活动记录失败: ${createRecordResult.message}`, createRecordResult.code);
        }
        actRecord = createRecordResult.data;
        activityData.globalActMgrInfo.set(actId, actRecord);
      }

      // 4. 更新进度
      const oldProgress = actRecord.progress;
      actRecord.progress = Math.max(actRecord.progress, progress);
      actRecord.updateTime = Date.now();

      if (data) {
        actRecord.data = { ...actRecord.data, ...data };
      }

      // 5. 保存到数据库
      const saveResult = await this.saveActivityData(activityData);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存活动数据失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`活动进度更新成功: ${characterId}, 活动: ${actId}, 进度: ${oldProgress} -> ${actRecord.progress}`);

      const responseData = {
        success: true,
        message: '活动进度更新成功',
        actId,
        oldProgress,
        progress: actRecord.progress,
        progressChanged: actRecord.progress !== oldProgress,
        updatedAt: actRecord.updateTime,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'update_activity_progress',
      metadata: { characterId, actId, progress }
    });
  }

  /**
   * 领取活动奖励
   * 已适配Result模式：涉及多重验证+奖励发放+数据保存，使用executeBusinessOperation包装
   * 基于old项目claimActReward方法
   */
  async claimActivityReward(characterId: string, actId: number, rewardId: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId || actId === undefined || actId === null || rewardId === undefined || rewardId === null) {
        return XResultUtils.error('参数不完整：characterId、actId和rewardId不能为空', 'INVALID_PARAMETERS');
      }

      if (actId < 0 || rewardId < 0) {
        return XResultUtils.error('活动ID或奖励ID无效', 'INVALID_ID');
      }

      // 1. 初始化活动数据
      const activityDataResult = await this.initActivityData(characterId);
      if (XResultUtils.isFailure(activityDataResult)) {
        return XResultUtils.error(`初始化活动数据失败: ${activityDataResult.message}`, activityDataResult.code);
      }

      const activityData = activityDataResult.data;

      // 2. 验证活动存在
      const actInfo = activityData.globalCurrActList.get(actId);
      if (!actInfo) {
        return XResultUtils.error('活动不存在', ErrorCode.ACTIVITY_NOT_FOUND);
      }

      const actRecord = activityData.globalActMgrInfo.get(actId);
      if (!actRecord) {
        return XResultUtils.error('活动记录不存在', 'ACTIVITY_RECORD_NOT_FOUND');
      }

      // 3. 检查是否已领取
      if (actRecord.rewards.includes(rewardId)) {
        return XResultUtils.failure('奖励已领取', 'REWARD_ALREADY_CLAIMED', {
          actId,
          rewardId,
          claimedRewards: actRecord.rewards
        });
      }

      // 4. 检查是否满足领取条件
      const canClaimResult = await this.checkRewardCondition(actRecord, rewardId, actInfo.config);
      if (XResultUtils.isFailure(canClaimResult)) {
        return XResultUtils.error(`检查奖励条件失败: ${canClaimResult.message}`, canClaimResult.code);
      }

      if (!canClaimResult.data) {
        return XResultUtils.failure('不满足奖励领取条件', 'REWARD_CONDITION_NOT_MET', {
          actId,
          rewardId,
          currentProgress: actRecord.progress,
          currentStatus: actRecord.status
        });
      }

      // 5. 获取奖励配置
      const rewardsResult = await this.getRewardConfig(actId, rewardId);
      if (XResultUtils.isFailure(rewardsResult)) {
        return XResultUtils.error(`获取奖励配置失败: ${rewardsResult.message}`, rewardsResult.code);
      }

      const rewards = rewardsResult.data;

      // 6. 发放奖励
      const distributeResult = await this.distributeActivityRewards(characterId, rewards);
      if (XResultUtils.isFailure(distributeResult)) {
        return XResultUtils.error(`发放奖励失败: ${distributeResult.message}`, distributeResult.code);
      }

      // 7. 记录已领取
      actRecord.rewards.push(rewardId);
      actRecord.updateTime = Date.now();

      // 8. 保存到数据库
      const saveResult = await this.saveActivityData(activityData);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存活动数据失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`活动奖励领取成功: ${characterId}, 活动: ${actId}, 奖励: ${rewardId}`);

      const responseData = {
        success: true,
        message: '活动奖励领取成功',
        actId,
        rewardId,
        rewards,
        rewardCount: rewards.length,
        claimedAt: actRecord.updateTime,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'claim_activity_reward',
      metadata: { characterId, actId, rewardId }
    });
  }

  /**
   * 首充激活
   * 已适配Result模式：简单的业务操作，不使用executeBusinessOperation
   * 基于old项目activeFirstCharge方法
   */
  async activateFirstCharge(characterId: string): Promise<XResult<any>> {
    // 参数验证
    if (!characterId) {
      return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
    }

    // 初始化活动数据
    const activityDataResult = await this.initActivityData(characterId);
    if (XResultUtils.isFailure(activityDataResult)) {
      return XResultUtils.error(`初始化活动数据失败: ${activityDataResult.message}`, activityDataResult.code);
    }

    const activityData = activityDataResult.data;

    // 检查首充状态
    if (activityData.firstChargeStatus === FirstChargeStatus.ACTIVE) {
      return XResultUtils.failure('首充已激活', 'FIRST_CHARGE_ALREADY_ACTIVE', {
        currentStatus: activityData.firstChargeStatus
      });
    }

    // 激活首充
    activityData.firstChargeStatus = FirstChargeStatus.ACTIVE;

    // 保存到数据库
    const saveResult = await this.saveActivityData(activityData);
    if (XResultUtils.isFailure(saveResult)) {
      return XResultUtils.error(`保存活动数据失败: ${saveResult.message}`, saveResult.code);
    }

    this.logger.log(`首充激活成功: ${characterId}`);

    const responseData = {
      success: true,
      message: '首充激活成功',
      activatedAt: Date.now(),
    };

    return XResultUtils.ok(responseData);
  }

  // ==================== 私有方法 - 已适配Result模式 ====================

  /**
   * 创建新的活动数据
   * 已适配Result模式：返回XResult类型，增加参数验证
   */
  private async createNewActivityData(characterId: string): Promise<XResult<ActivityData>> {
    if (!characterId) {
      return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
    }

    try {
      const activityData: ActivityData = {
        uid: characterId,
        globalCurrActList: new Map(),
        globalActMgrInfo: new Map(),
        historyActList: new Map(),
        firstChargeStatus: FirstChargeStatus.NONE,
        openChargeStatus: FirstChargeStatus.NONE,
      };

      return XResultUtils.ok(activityData);
    } catch (error) {
      return XResultUtils.error(`创建活动数据异常: ${error.message}`, 'CREATE_ACTIVITY_DATA_ERROR');
    }
  }

  /**
   * 加载全局活动
   * 已适配Result模式：返回XResult类型，增加错误处理和参数验证
   * 基于old项目loadGlobalAct方法
   */
  private async loadGlobalActivities(activityData: ActivityData): Promise<XResult<void>> {
    if (!activityData) {
      return XResultUtils.error('活动数据不能为空', 'INVALID_ACTIVITY_DATA');
    }

    try {
      // 从配置表加载活动控制数据
      const activityConfigsResult = await this.getActivityConfigs();
      if (XResultUtils.isFailure(activityConfigsResult)) {
        return XResultUtils.error(`获取活动配置失败: ${activityConfigsResult.message}`, activityConfigsResult.code);
      }

      const activityConfigs = activityConfigsResult.data;
      const now = Date.now();

      for (const config of activityConfigs) {
        const actId = config.Id;
        if (actId <= 0) continue;

        const actType = config.ActivityType;
        const timeType = config.TimeType;
        const periods = config.Periods;

        let startTime = 0;
        let endTime = 0;

        // 计算活动时间
        if (timeType === ActivityTimeType.PERSISTENT) {
          // 持久性活动
          const refreshCycle = config.RefreshCycle;
          const timeRange = this.calculatePersistentTime(refreshCycle, now);
          startTime = timeRange.startTime;
          endTime = timeRange.endTime;
        } else if (timeType === ActivityTimeType.CYCLE) {
          // 周期性活动
          startTime = this.timeToUnixTime(config.StartTime);
          endTime = this.timeToUnixTime(config.EndTime);

          // 检查活动是否过期
          if (now < startTime || now > endTime) {
            // 清理过期活动数据
            if (actType !== ActivityType.FIRST_CHARGE) {
              activityData.globalCurrActList.delete(actId);
              activityData.globalActMgrInfo.delete(actId);
              continue;
            }
          }
        }

        // 处理特殊活动类型
        if (actType === ActivityType.FIRST_CHARGE) {
          if (activityData.firstChargeStatus === FirstChargeStatus.ACTIVE) {
            continue; // 已激活，不显示
          }
          endTime = this.getMaxTime(); // 设置最大时间
        }

        if (actType === ActivityType.OPEN_GIFT_BAG) {
          if (activityData.openChargeStatus === FirstChargeStatus.ACTIVE) {
            continue; // 已激活，不显示
          }
        }

        // 检查期数变化
        if (timeType === ActivityTimeType.CYCLE) {
          const existingRecord = activityData.globalActMgrInfo.get(actId);
          if (existingRecord && existingRecord.periods !== periods) {
            // 期数变化，清理数据
            activityData.globalCurrActList.delete(actId);
            activityData.globalActMgrInfo.delete(actId);
          }
        }

        // 创建或更新活动信息
        if (!activityData.globalCurrActList.has(actId)) {
          const actInfoResult = await this.createActivityInfo(actId, config, startTime, endTime);
          if (XResultUtils.isFailure(actInfoResult)) {
            this.logger.warn(`创建活动信息失败: ${actId}, ${actInfoResult.message}`);
            continue;
          }
          const actInfo = actInfoResult.data;
          activityData.globalCurrActList.set(actId, actInfo);

          if (!activityData.globalActMgrInfo.has(actId)) {
            const actRecordResult = await this.createActivityRecord(actId, actType, periods);
            if (XResultUtils.isFailure(actRecordResult)) {
              this.logger.warn(`创建活动记录失败: ${actId}, ${actRecordResult.message}`);
              continue;
            }
            const actRecord = actRecordResult.data;
            activityData.globalActMgrInfo.set(actId, actRecord);
          }
        }
      }

      return XResultUtils.ok(undefined);
    } catch (error) {
      return XResultUtils.error(`加载全局活动异常: ${error.message}`, 'LOAD_GLOBAL_ACTIVITIES_ERROR');
    }
  }

  /**
   * 检查并重新加载活动控制
   * 已适配Result模式：返回XResult类型，增加错误处理和参数验证
   */
  private async checkAndReloadActivityControl(activityData: ActivityData): Promise<XResult<void>> {
    if (!activityData) {
      return XResultUtils.error('活动数据不能为空', 'INVALID_ACTIVITY_DATA');
    }

    try {
      const delActList: number[] = [];
      const currTime = Date.now();

      for (const [actId, actInfo] of activityData.globalCurrActList) {
        // 检查配置表中是否还存在该活动
        const configResult = await this.getActivityConfig(actId);
        if (XResultUtils.isFailure(configResult)) {
          this.logger.warn(`获取活动配置失败: ${actId}, ${configResult.message}`);
          delActList.push(actId);
          continue;
        }

        const config = configResult.data;
        if (!config) {
          delActList.push(actId);
          continue;
        }

        // 持久化活动不需要删除
        if (config.TimeType === ActivityTimeType.PERSISTENT) {
          continue;
        }

        // 检查活动是否过期
        if (currTime > actInfo.endTime) {
          delActList.push(actId);
        }
      }

      // 删除过期活动
      for (const actId of delActList) {
        activityData.globalCurrActList.delete(actId);
        activityData.globalActMgrInfo.delete(actId);
      }

      if (delActList.length > 0) {
        this.logger.log(`清理过期活动: ${delActList.join(', ')}`);
      }

      return XResultUtils.ok(undefined);
    } catch (error) {
      return XResultUtils.error(`检查活动控制异常: ${error.message}`, 'CHECK_ACTIVITY_CONTROL_ERROR');
    }
  }

  /**
   * 创建活动信息
   * 已适配Result模式：返回XResult类型，增加参数验证
   */
  private async createActivityInfo(actId: number, config: any, startTime: number, endTime: number): Promise<XResult<ActivityInfo>> {
    if (actId === undefined || actId === null || actId < 0) {
      return XResultUtils.error('活动ID无效', 'INVALID_ACTIVITY_ID');
    }

    if (!config) {
      return XResultUtils.error('活动配置不能为空', 'INVALID_ACTIVITY_CONFIG');
    }

    if (startTime < 0 || endTime < 0 || endTime <= startTime) {
      return XResultUtils.error('活动时间无效', 'INVALID_ACTIVITY_TIME');
    }

    try {
      const activityInfo: ActivityInfo = {
        actId,
        actType: config.ActivityType,
        actName: config.ActivityName || `活动${actId}`,
        startTime,
        endTime,
        timeType: config.TimeType,
        refreshCycle: config.RefreshCycle || 0,
        periods: config.Periods || 1,
        isActive: true,
        config,
      };

      return XResultUtils.ok(activityInfo);
    } catch (error) {
      return XResultUtils.error(`创建活动信息异常: ${error.message}`, 'CREATE_ACTIVITY_INFO_ERROR');
    }
  }

  /**
   * 创建活动记录
   * 已适配Result模式：返回XResult类型，增加参数验证
   */
  private async createActivityRecord(actId: number, actType: ActivityType, periods: number): Promise<XResult<ActivityRecord>> {
    if (actId === undefined || actId === null || actId < 0) {
      return XResultUtils.error('活动ID无效', 'INVALID_ACTIVITY_ID');
    }

    if (!actType) {
      return XResultUtils.error('活动类型无效', 'INVALID_ACTIVITY_TYPE');
    }

    if (periods === undefined || periods === null || periods < 0) {
      return XResultUtils.error('期数无效', 'INVALID_PERIODS');
    }

    try {
      const now = Date.now();
      const activityRecord: ActivityRecord = {
        actId,
        actType,
        periods,
        progress: 0,
        status: 0,
        rewards: [],
        data: {},
        createTime: now,
        updateTime: now,
      };

      return XResultUtils.ok(activityRecord);
    } catch (error) {
      return XResultUtils.error(`创建活动记录异常: ${error.message}`, 'CREATE_ACTIVITY_RECORD_ERROR');
    }
  }

  /**
   * 计算持久性活动时间
   * 纯数字运算，保持原有简洁性，不使用Result模式包装
   */
  private calculatePersistentTime(refreshCycle: ActivityRefreshType, now: number): { startTime: number; endTime: number } {
    switch (refreshCycle) {
      case ActivityRefreshType.CROSS_DAY:
        return {
          startTime: this.beginningOfToday(now),
          endTime: this.endingOfToday(now),
        };
      case ActivityRefreshType.CROSS_WEEK:
        return {
          startTime: this.getWeekStartTime(now),
          endTime: this.getWeekEndTime(now),
        };
      case ActivityRefreshType.CROSS_MONTH:
        return {
          startTime: this.getMonthStartTime(now),
          endTime: this.getMonthEndTime(now),
        };
      default:
        return { startTime: now, endTime: now };
    }
  }

  /**
   * 检查奖励领取条件
   * 已适配Result模式：返回XResult类型，增加参数验证
   */
  private async checkRewardCondition(actRecord: ActivityRecord, rewardId: number, config: any): Promise<XResult<boolean>> {
    if (!actRecord) {
      return XResultUtils.error('活动记录不能为空', 'INVALID_ACTIVITY_RECORD');
    }

    if (rewardId === undefined || rewardId === null || rewardId < 0) {
      return XResultUtils.error('奖励ID无效', 'INVALID_REWARD_ID');
    }

    if (!config) {
      return XResultUtils.error('活动配置不能为空', 'INVALID_ACTIVITY_CONFIG');
    }

    try {
      let canClaim = false;

      // 根据活动类型和配置检查领取条件
      switch (config.actType) {
        case ActivityType.DAILY_TASK:
        case ActivityType.WEEKLY_TASK:
          // 任务类活动：检查进度是否达到要求
          canClaim = actRecord.progress >= (config.targetProgress || rewardId);
          break;
        case ActivityType.ACHIEVEMENT:
          // 成就类活动：检查是否完成
          canClaim = actRecord.status === 1; // 1表示已完成
          break;
        case ActivityType.FIRST_CHARGE:
          // 首充活动：检查是否已充值
          canClaim = actRecord.data?.hasCharged === true;
          break;
        default:
          // 默认逻辑：检查进度
          canClaim = actRecord.progress >= rewardId;
          break;
      }

      return XResultUtils.ok(canClaim);
    } catch (error) {
      return XResultUtils.error(`检查奖励条件异常: ${error.message}`, 'CHECK_REWARD_CONDITION_ERROR');
    }
  }

  /**
   * 获取奖励配置
   * 已适配Result模式：返回XResult类型，增加错误处理和参数验证
   */
  private async getRewardConfig(actId: number, rewardId: number): Promise<XResult<any[]>> {
    if (actId === undefined || actId === null || actId < 0) {
      return XResultUtils.error('活动ID无效', 'INVALID_ACTIVITY_ID');
    }

    if (rewardId === undefined || rewardId === null || rewardId < 0) {
      return XResultUtils.error('奖励ID无效', 'INVALID_REWARD_ID');
    }

    try {
      // 先获取活动配置
      const actConfigResult = await this.getActivityConfig(actId);
      if (XResultUtils.isFailure(actConfigResult)) {
        return XResultUtils.error(`获取活动配置失败: ${actConfigResult.message}`, actConfigResult.code);
      }

      const actConfig = actConfigResult.data;

      // 根据活动类型选择对应的奖励配置表
      let rewardConfig = null;
      if (actConfig && actConfig.activityType === ActivityType.FIRST_CHARGE) {
        // 首充活动使用turntableReward配置表
        rewardConfig = await this.gameConfig.turntableReward.get(rewardId);
      } else {
        // 其他活动类型也使用turntableReward作为通用奖励配置
        rewardConfig = await this.gameConfig.turntableReward.get(rewardId);
      }

      if (!rewardConfig) {
        this.logger.warn(`奖励配置不存在: actId=${actId}, rewardId=${rewardId}`);
        return XResultUtils.ok([]); // 返回空数组而不是错误
      }

      // 转换为标准奖励格式
      const rewards = [];
      if (rewardConfig) {
        rewards.push({
          itemType: rewardConfig.type || 1, // 1=道具，2=货币，3=球员
          resId: rewardConfig.itemId, // 使用itemId字段
          num: rewardConfig.num || 1,
          name: rewardConfig.itemName || '',
        });
      }

      return XResultUtils.ok(rewards);
    } catch (error) {
      return XResultUtils.error(`获取奖励配置异常: ${error.message}`, 'GET_REWARD_CONFIG_ERROR');
    }
  }

  // 时间工具方法
  private timeToUnixTime(timeStr: string): number {
    return new Date(timeStr).getTime();
  }

  private getMaxTime(): number {
    return new Date('2099-12-31').getTime();
  }

  private beginningOfToday(timestamp: number): number {
    const date = new Date(timestamp);
    date.setHours(0, 0, 0, 0);
    return date.getTime();
  }

  private endingOfToday(timestamp: number): number {
    const date = new Date(timestamp);
    date.setHours(23, 59, 59, 999);
    return date.getTime();
  }

  private getWeekStartTime(timestamp: number): number {
    const date = new Date(timestamp);
    const day = date.getDay();
    const diff = date.getDate() - day + (day === 0 ? -6 : 1);
    const monday = new Date(date.setDate(diff));
    monday.setHours(0, 0, 0, 0);
    return monday.getTime();
  }

  private getWeekEndTime(timestamp: number): number {
    const startTime = this.getWeekStartTime(timestamp);
    return startTime + (7 * 24 * 60 * 60 * 1000) - 1;
  }

  private getMonthStartTime(timestamp: number): number {
    const date = new Date(timestamp);
    return new Date(date.getFullYear(), date.getMonth(), 1).getTime();
  }

  private getMonthEndTime(timestamp: number): number {
    const date = new Date(timestamp);
    return new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59, 999).getTime();
  }

  // 数据访问方法 - 已适配Result模式
  /**
   * 获取活动数据
   * 已适配Result模式：返回XResult类型，增加错误处理
   */
  private async getActivityData(characterId: string): Promise<XResult<ActivityData | null>> {
    if (!characterId) {
      return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
    }

    try {
      const activityResult = await this.eventRepository.findByCharacterId(characterId);
      if (XResultUtils.isFailure(activityResult)) {
        return XResultUtils.error(`查询活动数据失败: ${activityResult.message}`, activityResult.code);
      }

      const activity = activityResult.data;
      if (!activity) {
        return XResultUtils.ok(null);
      }

      // 转换为业务数据格式
      const globalCurrActList = new Map<number, ActivityInfo>();
      if (activity.globalCurrActList && Array.isArray(activity.globalCurrActList)) {
        activity.globalCurrActList.forEach(act => {
          if (act && typeof act.actId === 'number') {
            globalCurrActList.set(act.actId, {
              actId: act.actId,
              actType: act.actType,
              actName: act.actName,
              startTime: act.startTime,
              endTime: act.endTime,
              timeType: act.timeType,
              refreshCycle: act.refreshCycle,
              periods: act.periods,
              isActive: act.isActive,
              config: act.config,
            });
          }
        });
      }

      const globalActMgrInfo = new Map<number, ActivityRecord>();
      if (activity.globalActMgrInfo && Array.isArray(activity.globalActMgrInfo)) {
        activity.globalActMgrInfo.forEach(record => {
          if (record && typeof record.actId === 'number') {
            globalActMgrInfo.set(record.actId, {
              actId: record.actId,
              actType: record.actType,
              periods: record.periods,
              progress: record.progress,
              status: record.status,
              rewards: record.rewards,
              data: record.data,
              createTime: record.createTime,
              updateTime: record.updateTime,
            });
          }
        });
      }

      const activityData: ActivityData = {
        uid: activity.uid,
        globalCurrActList,
        globalActMgrInfo,
        historyActList: this.convertHistoryActivityData(activity.historyActList),
        firstChargeStatus: activity.firstChargeStatus,
        openChargeStatus: activity.openChargeStatus,
      };

      return XResultUtils.ok(activityData);
    } catch (error) {
      return XResultUtils.error(`获取活动数据异常: ${error.message}`, 'GET_ACTIVITY_DATA_ERROR');
    }
  }

  /**
   * 保存活动数据
   * 已适配Result模式：返回XResult类型，增加错误处理
   */
  private async saveActivityData(activityData: ActivityData): Promise<XResult<void>> {
    if (!activityData || !activityData.uid) {
      return XResultUtils.error('活动数据或角色ID不能为空', 'INVALID_ACTIVITY_DATA');
    }

    try {
      // 转换为数据库格式
      const globalCurrActList = Array.from(activityData.globalCurrActList.values());
      const globalActMgrInfo = Array.from(activityData.globalActMgrInfo.values());

      // 检查是否存在记录
      const existingActivityResult = await this.eventRepository.findByCharacterId(activityData.uid);
      if (XResultUtils.isFailure(existingActivityResult)) {
        return XResultUtils.error(`查询现有活动记录失败: ${existingActivityResult.message}`, existingActivityResult.code);
      }

      const existingActivity = existingActivityResult.data;

      if (existingActivity) {
        // 更新现有记录
        const updateResult = await this.eventRepository.update(activityData.uid, {
          globalCurrActList,
          globalActMgrInfo,
          firstChargeStatus: activityData.firstChargeStatus,
          openChargeStatus: activityData.openChargeStatus,
        });

        if (XResultUtils.isFailure(updateResult)) {
          return XResultUtils.error(`更新活动数据失败: ${updateResult.message}`, updateResult.code);
        }
      } else {
        // 创建新记录
        const createResult = await this.eventRepository.create({
          activityRecordId: this.generateActivityRecordId(),
          uid: activityData.uid,
          serverId: this.getServerId(), // 从环境变量或上下文获取
          globalCurrActList,
          globalActMgrInfo,
          firstChargeStatus: activityData.firstChargeStatus,
          openChargeStatus: activityData.openChargeStatus,
        });

        if (XResultUtils.isFailure(createResult)) {
          return XResultUtils.error(`创建活动数据失败: ${createResult.message}`, createResult.code);
        }
      }

      return XResultUtils.ok(undefined);
    } catch (error) {
      return XResultUtils.error(`保存活动数据异常: ${error.message}`, 'SAVE_ACTIVITY_DATA_ERROR');
    }
  }

  private async getActivityConfigs(): Promise<XResult<any[]>> {
    try {
      // 从ActivityControl配置表获取活动配置（基于old项目ActivityControl）
      // 从ActiveControl配置表获取活动配置（基于old项目ActiveControl）
      const configs = await this.gameConfig.activeControl.getAll();
      return configs || [];
    } catch (error) {
      this.logger.error('获取活动配置失败', error);
      return [];
    }
  }

  private async getActivityConfig(actId: number): Promise<XResult<any | null>> {
    try {
      // 从ActivityControl配置表获取单个活动配置（基于old项目ActivityControl）
      // 从ActiveControl配置表获取单个活动配置（基于old项目ActiveControl）
      const config = await this.gameConfig.activeControl.get(actId);
      return config || null;
    } catch (error) {
      this.logger.error('获取单个活动配置失败', error);
      return null;
    }
  }

  private generateActivityRecordId(): string {
    return `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 最佳11人抽奖
   * 严格基于old项目: Act.prototype.buyBestFootball
   */
  async buyBestFootball(characterId: string, index: number): Promise<XResult<any>> {
    this.logger.log(`最佳11人抽奖: ${characterId}, 类型: ${index}`);

    try {
      // 参数校验
      if (index !== 1 && index !== 2) {
        return {
          code: -1,
          message: '抽奖类型参数错误',
          data: null,
        };
      }

      // 计算费用（基于old项目逻辑）
      let needGold = 0;
      if (index === 1) {
        // 单抽费用 - SystemParam[9011]
        const config = await this.gameConfig.systemParam.get(9011);
        needGold = config?.parameter || 50000;
      } else {
        // 十连抽费用
        const config = await this.gameConfig.systemParam.get(9011);
        needGold = (config?.parameter || 50000) * 10;
      }

      // 检查角色金币是否足够
      const goldCheckResult = await this.checkCharacterGold(characterId, needGold);
      if (!goldCheckResult.success) {
        return {
          code: -2,
          message: '金币不足',
          data: {
            required: needGold,
            current: goldCheckResult.currentGold,
          },
        };
      }

      // 执行抽奖逻辑
      const lotteryResult = await this.executeBestFootballLottery(characterId, index);
      if (lotteryResult.code !== 0) {
        return lotteryResult;
      }

      // 扣除金币
      const deductResult = await this.deductCharacterGold(characterId, needGold);
      if (!deductResult.success) {
        return {
          code: -3,
          message: '扣除金币失败',
          data: null,
        };
      }

      // 发放奖励
      await this.giveItemRewards(characterId, lotteryResult.data.itemList);

      return {
        code: 0,
        message: '抽奖成功',
        data: {
          itemList: lotteryResult.data.itemList,
          cost: needGold,
        },
      };

    } catch (error) {
      this.logger.error('最佳11人抽奖失败', error);
      return {
        code: -4,
        message: '抽奖系统异常',
        data: null,
      };
    }
  }

  /**
   * 老虎机抽奖
   * 严格基于old项目: Act.prototype.buyTurntable
   */
  async buyTurntable(characterId: string, frequencyType: number): Promise<XResult<any>> {
    this.logger.log(`老虎机抽奖: ${characterId}, 类型: ${frequencyType}`);

    try {
      // 参数校验
      if (frequencyType !== 1 && frequencyType !== 2) {
        return {
          code: -1,
          message: '抽奖类型参数错误',
          data: null,
        };
      }

      // 获取活动配置
      const turntableConfig = await this.gameConfig.turntableControl.get(frequencyType);
      if (!turntableConfig) {
        return {
          code: -2,
          message: '老虎机配置获取失败',
          data: null,
        };
      }

      // 检查免费次数（基于old项目免费次数逻辑）
      const freeTimesResult = await this.checkTurntableFreeTimes(characterId, frequencyType);
      if (freeTimesResult.hasFreeTime) {
        // 有免费次数，直接执行抽奖
        const lotteryResult = await this.executeTurntableLottery(characterId, frequencyType);
        if (lotteryResult.code === 0) {
          // 扣除免费次数
          await this.deductTurntableFreeTimes(characterId, frequencyType, 1);
        }
        return lotteryResult;
      }

      // 检查金币是否足够
      const needGold = turntableConfig.money || 0;
      const goldCheckResult = await this.checkCharacterGold(characterId, needGold);
      if (!goldCheckResult.success) {
        return {
          code: -3,
          message: '金币不足',
          data: {
            required: needGold,
            current: goldCheckResult.currentGold,
          },
        };
      }

      // 执行老虎机抽奖
      const lotteryResult = await this.executeTurntableLottery(characterId, frequencyType);
      if (lotteryResult.code !== 0) {
        return lotteryResult;
      }

      // 扣除金币
      const deductResult = await this.deductCharacterGold(characterId, needGold);
      if (!deductResult.success) {
        return {
          code: -4,
          message: '扣除金币失败',
          data: null,
        };
      }

      return {
        code: 0,
        message: '老虎机抽奖成功',
        data: lotteryResult.data,
      };

    } catch (error) {
      this.logger.error('老虎机抽奖失败', error);
      return {
        code: -5,
        message: '抽奖系统异常',
        data: null,
      };
    }
  }

  /**
   * 检查角色金币是否足够
   */
  private async checkCharacterGold(characterId: string, amount: number): Promise<XResult<any>> {
    try {
      // 调用Character服务获取角色信息
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.getInfo',
        {
          characterId,
          serverId: 'server_001',
        }
      );

      if (!result || result.code !== 0) {
        return {
          success: false,
          currentGold: 0,
          message: '获取角色信息失败',
        };
      }

      const currentGold = result.data?.gold || 0;
      const hasEnough = currentGold >= amount;

      return {
        success: hasEnough,
        currentGold,
        required: amount,
        message: hasEnough ? '金币充足' : '金币不足',
      };
    } catch (error) {
      this.logger.error('检查角色金币失败', error);
      return {
        success: false,
        currentGold: 0,
        message: '检查金币失败',
      };
    }
  }

  /**
   * 扣除角色金币
   */
  private async deductCharacterGold(characterId: string, amount: number): Promise<XResult<any>> {
    try {
      // 调用Character服务扣除金币
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.currency.subtract',
        {
          characterId,
          currencyDto: {
            currencyType: 'gold',
            amount,
            reason: 'activity_lottery',
          },
          serverId: 'server_001',
        }
      );

      if (!result || result.code !== 0) {
        return {
          success: false,
          message: '扣除金币失败',
        };
      }

      return {
        success: true,
        newBalance: result.data?.newBalance || 0,
        message: '扣除金币成功',
      };
    } catch (error) {
      this.logger.error('扣除角色金币失败', error);
      return {
        success: false,
        message: '扣除金币失败',
      };
    }
  }

  /**
   * 执行最佳11人抽奖逻辑
   * 基于old项目的randomBestFootball方法
   */
  private async executeBestFootballLottery(characterId: string, index: number): Promise<XResult<any>> {
    try {
      // 实现保底机制和抽奖逻辑（基于old项目保底机制）
      const guaranteeResult = await this.checkBestFootballGuarantee(characterId);
      if (guaranteeResult.shouldTrigger) {
        // 触发保底，返回保底奖励
        const guaranteeItems = await this.getGuaranteeRewards(guaranteeResult.guaranteeType);
        await this.resetBestFootballGuarantee(characterId);
        return {
          code: 0,
          data: {
            itemList: guaranteeItems,
            isGuarantee: true,
          },
        };
      }
      const itemList = [];

      if (index === 1) {
        // 单抽
        const randomItem = await this.randomBestFootball(1, 1, 'BestFootball');
        itemList.push(...randomItem);
      } else {
        // 十连抽
        const randomItems = await this.randomBestFootball(1, 11, 'BestFootball');
        itemList.push(...randomItems);
      }

      return {
        code: 0,
        data: {
          itemList,
        },
      };
    } catch (error) {
      this.logger.error('执行最佳11人抽奖失败', error);
      return {
        code: -1,
        message: '抽奖执行失败',
        data: null,
      };
    }
  }

  /**
   * 执行老虎机抽奖逻辑
   * 基于old项目的老虎机抽奖实现
   */
  private async executeTurntableLottery(characterId: string, frequencyType: number): Promise<XResult<any>> {
    try {
      const itemIdList = [];
      let status = 0; // 0=普通, 1=小王, 2=大王

      if (frequencyType === 1) {
        // 单抽
        const result = await this.randomTurntable(characterId);
        itemIdList.push(...result.itemIdList);
        status = result.status;
      } else {
        // 连抽
        for (let i = 0; i < 10; i++) {
          const result = await this.randomTurntable(characterId);
          itemIdList.push(...result.itemIdList);
          if (result.status > status) {
            status = result.status; // 取最高状态
          }
        }
      }

      return {
        code: 0,
        data: {
          itemIdList,
          status,
        },
      };
    } catch (error) {
      this.logger.error('执行老虎机抽奖失败', error);
      return {
        code: -1,
        message: '老虎机抽奖执行失败',
        data: null,
      };
    }
  }

  /**
   * 老虎机随机算法
   * 严格基于old项目: Act.prototype.randomTurntable
   */
  private async randomTurntable(characterId: string): Promise<XResult<any>> {
    try {
      // 获取老虎机奖励配置
      const turntableRewards = await this.gameConfig.turntableReward.getAll();

      if (turntableRewards.length === 0) {
        return {
          itemIdList: [],
          status: 0,
        };
      }

      // 计算总权重
      let totalWeight = 0;
      for (const config of turntableRewards) {
        totalWeight += config.weight || 0;
      }

      if (totalWeight === 0) {
        return {
          itemIdList: [],
          status: 0,
        };
      }

      // 生成随机数
      const randNum = Math.floor(Math.random() * totalWeight) + 1;
      let currentWeight = 0;

      // 权重随机选择
      for (const config of turntableRewards) {
        currentWeight += config.weight || 0;

        if (currentWeight >= randNum) {
          // 根据配置类型确定状态
          let status = 0;
          if (config.type === 1) {
            status = 1; // 小王
          } else if (config.type === 2) {
            status = 2; // 大王
          }

          // 获取奖励物品
          const itemIdList = [];
          if (config.itemId && config.itemId > 0) {
            itemIdList.push(config.itemId);
          }

          this.logger.debug(`老虎机抽奖结果: 物品${config.itemId}, 状态: ${status}`);

          return {
            itemIdList,
            status,
          };
        }
      }

      // 兜底返回
      const firstConfig = turntableRewards[0];
      return {
        itemIdList: firstConfig.itemId ? [firstConfig.itemId] : [],
        status: 0,
      };
    } catch (error) {
      this.logger.error('老虎机随机算法失败', error);
      return {
        itemIdList: [],
        status: 0,
      };
    }
  }

  /**
   * 执行拉霸抽奖逻辑
   * 基于old项目的拉霸抽奖实现
   */
  private async executeSlotsLottery(
    characterId: string,
    frequencyType: number,
    securityMoney: number,
    actId: number
  ): Promise<XResult<any>> {
    try {
      const itemIdList = [];
      let currentSecurityMoney = securityMoney;

      if (frequencyType === 1) {
        // 单抽
        const result = await this.randomSlots(actId, currentSecurityMoney,characterId);
        const groupId = result.groupId;
        currentSecurityMoney = result.securityMoney;

        const awardObj = await this.getWeightGroupIdAward('PullerAward', groupId);
        itemIdList.push(awardObj);
      } else {
        // 十连抽（买10送1）
        for (let i = 0; i < 11; i++) {
          const result = await this.randomSlots(actId, currentSecurityMoney,characterId);
          const groupId = result.groupId;
          currentSecurityMoney = result.securityMoney;

          const awardObj = await this.getWeightGroupIdAward('PullerAward', groupId);
          itemIdList.push(awardObj);
        }
      }

      return {
        code: 0,
        data: {
          itemIdList,
          status: 0,
          securityMoney: currentSecurityMoney,
        },
      };
    } catch (error) {
      this.logger.error('执行拉霸抽奖失败', error);
      return {
        code: -1,
        message: '拉霸抽奖执行失败',
        data: null,
      };
    }
  }

  /**
   * 拉霸随机算法
   * 严格基于old项目: Act.prototype.randomSlots
   */
  private async randomSlots(actId: number, securityMoney: number, characterId?: string): Promise<XResult<any>> {
    try {
      // 获取保底配置
      const guaranteeNumConfig = await this.gameConfig.systemParam.get(9021); // SLOTS_GUARANTEE_NUM
      const guaranteeNum = guaranteeNumConfig?.parameter || 100;

      const clearMoneyConfig = await this.gameConfig.systemParam.get(9022); // SLOTS_GUARANTEE_MONEY
      const clearMoney = clearMoneyConfig?.parameter || 1000000;

      // 获取活动记录中的累计抽奖次数（基于old项目活动记录）
      let currentDrawCount = 0;
      if (characterId) {
        const activityData = await this.getActivityData(characterId);
        const actRecord = activityData?.globalActMgrInfo.get(actId);
        currentDrawCount = actRecord?.data?.slotsDrawCount || 0;
      }

      // 保底机制检查
      if (currentDrawCount >= guaranteeNum) {
        // 触发保底
        return {
          groupId: 3, // 保底组
          securityMoney: securityMoney,
        };
      }

      // 安全金额保底检查
      if (securityMoney >= clearMoney &&
          currentDrawCount > (guaranteeNum / 3) &&
          currentDrawCount < (guaranteeNum - guaranteeNum / 3)) {
        return {
          groupId: 3, // 保底组
          securityMoney: 0, // 清空安全金额
        };
      }

      // 正常权重抽奖
      const pullerAwardConfigs = await this.gameConfig.pullerAward.getAll();
      const groupWeights = new Map();

      // 计算各组权重（排除保底组3）
      for (const config of pullerAwardConfigs) {
        if (config.group === 3) continue; // 跳过保底组

        const groupId = config.group;
        const weight = config.weight;

        if (!groupWeights.has(groupId)) {
          groupWeights.set(groupId, weight);
        }
      }

      // 权重随机选择
      const groupList = [];
      for (const [groupId, weight] of groupWeights) {
        for (let i = 0; i < weight; i++) {
          groupList.push(groupId);
        }
      }

      // 随机排序
      groupList.sort(() => 0.5 - Math.random());

      const selectedGroupId = groupList[Math.floor(Math.random() * groupList.length)];

      return {
        groupId: selectedGroupId || 1,
        securityMoney: securityMoney,
      };
    } catch (error) {
      this.logger.error('拉霸随机算法失败', error);
      return {
        groupId: 1,
        securityMoney: securityMoney,
      };
    }
  }

  /**
   * 根据权重组ID获取奖励
   * 基于old项目: getWeightGrepIdAward
   */
  private async getWeightGroupIdAward(tableName: string, groupId: number): Promise<XResult<any>> {
    try {
      // 获取指定组的奖励配置
      const configs = await this.gameConfig.pullerAward.getAll();
      const groupConfigs = configs.filter(config => config.group === groupId);

      if (groupConfigs.length === 0) {
        return { itemId: 0, num: 0, id: 0 };
      }

      // 权重随机选择
      const randomList = [];
      for (const config of groupConfigs) {
        for (let i = 0; i < config.weight; i++) {
          randomList.push({
            itemId: config.itemId,
            num: config.num,
            resId: config.id,
          });
        }
      }

      const selectedItem = randomList[Math.floor(Math.random() * randomList.length)];

      return {
        itemId: selectedItem.itemId,
        num: selectedItem.num,
        id: selectedItem.resId,
      };
    } catch (error) {
      this.logger.error('获取权重组奖励失败', error);
      return { itemId: 0, num: 0, id: 0 };
    }
  }

  /**
   * 随机抽奖算法
   * 基于old项目: Act.prototype.randomBestFootball
   */
  private async randomBestFootball(type: number, randomNum: number, tableName: string): Promise<XResult<number[]>> {
    try {
      // 从配置表获取真实的抽奖配置（基于old项目抽奖配置表）
      // 根据tableName选择对应的抽奖配置表（基于old项目randomBestFootball方法）
      let lotteryConfig = null;
      if (tableName === 'BestFootball') {
        lotteryConfig = await this.gameConfig.bestFootball.getAll();
      } else if (tableName === 'BackWeekend') {
        lotteryConfig = await this.gameConfig.backWeekend.getAll();
      } else {
        // 默认使用BestFootball配置表
        lotteryConfig = await this.gameConfig.bestFootball.getAll();
      }
      if (!lotteryConfig || !Array.isArray(lotteryConfig)) {
        this.logger.warn(`抽奖配置不存在: ${tableName}`);
        return [];
      }

      // 基于old项目randomBestFootball方法实现权重抽奖
      const itemList = [];
      for (let i = 0; i < randomNum; i++) {
        const randomItem = this.weightedRandomSelect(lotteryConfig);
        if (randomItem) {
          itemList.push(randomItem.id); // 使用id字段而不是itemId
        }
      }

      return itemList;
    } catch (error) {
      this.logger.error('随机抽奖算法失败', error);
      return [];
    }
  }

  /**
   * 拉霸抽奖
   * 严格基于old项目: Act.prototype.buySlots
   */
  async buySlots(characterId: string, frequencyType: number, securityMoney: number = 0): Promise<XResult<any>> {
    this.logger.log(`拉霸抽奖: ${characterId}, 类型: ${frequencyType}, 保底金额: ${securityMoney}`);

    try {
      // 参数校验
      if (frequencyType !== 1 && frequencyType !== 2) {
        return {
          code: -1,
          message: '抽奖类型参数错误',
          data: null,
        };
      }

      // 获取活动配置
      const activityConfigs = await this.gameConfig.activeControl.getAll();
      const slotsActivity = activityConfigs.find(config =>
        config.activityType === 3 // ACT_TYPE_SLOTS
      );

      if (!slotsActivity) {
        return {
          code: -2,
          message: '拉霸活动未开启',
          data: null,
        };
      }

      // 检查免费次数（基于old项目免费次数逻辑）
      const freeTimesResult = await this.checkSlotsFreeTimes(characterId, frequencyType);
      if (freeTimesResult.hasFreeTime) {
        // 有免费次数，直接执行抽奖
        const lotteryResult = await this.executeSlotsLottery(characterId, frequencyType, slotsActivity.id, 0);
        if (lotteryResult.code === 0) {
          // 扣除免费次数
          await this.deductSlotsFreeTimes(characterId, frequencyType, 1);
        }
        return lotteryResult;
      }

      // 获取拉霸费用配置
      let needGold = 0;
      if (frequencyType === 1) {
        // 单抽费用
        const config = await this.gameConfig.systemParam.get(9019); // SLOTS_SINGLE_COST
        needGold = config?.parameter || 10000;
      } else {
        // 十连抽费用
        const config = await this.gameConfig.systemParam.get(9020); // SLOTS_TEN_COST
        needGold = config?.parameter || 90000;
      }

      // 检查金币是否足够
      const goldCheckResult = await this.checkCharacterGold(characterId, needGold);
      if (!goldCheckResult.success) {
        return {
          code: -3,
          message: '金币不足',
          data: {
            required: needGold,
            current: goldCheckResult.currentGold,
          },
        };
      }

      // 执行拉霸抽奖
      const lotteryResult = await this.executeSlotsLottery(
        characterId,
        frequencyType,
        securityMoney,
        slotsActivity.id
      );

      if (lotteryResult.code !== 0) {
        return lotteryResult;
      }

      // 扣除金币
      const deductResult = await this.deductCharacterGold(characterId, needGold);
      if (!deductResult.success) {
        return {
          code: -4,
          message: '扣除金币失败',
          data: null,
        };
      }

      return {
        code: 0,
        message: '拉霸抽奖成功',
        data: {
          itemIdList: lotteryResult.data.itemIdList,
          status: lotteryResult.data.status,
          securityMoney: lotteryResult.data.securityMoney,
          cost: needGold,
        },
      };

    } catch (error) {
      this.logger.error('拉霸抽奖失败', error);
      return {
        code: -5,
        message: '抽奖系统异常',
        data: null,
      };
    }
  }

  /**
   * 周末返场抽奖
   * 严格基于old项目: Act.prototype.weekDayEncore
   */
  async weekDayEncore(characterId: string): Promise<XResult<any>> {
    this.logger.log(`周末返场抽奖: ${characterId}`);

    try {
      // 获取周末返场费用
      const config = await this.gameConfig.systemParam.get(9018);
      const needGold = config?.parameter || 20000;

      // 检查金币是否足够
      const goldCheckResult = await this.checkCharacterGold(characterId, needGold);
      if (!goldCheckResult.success) {
        return {
          code: -1,
          message: '金币不足',
          data: {
            required: needGold,
            current: goldCheckResult.currentGold,
          },
        };
      }

      // 执行周末返场抽奖
      const randomList = await this.randomBestFootball(1, 1, 'BackWeekend');
      if (randomList.length === 0) {
        return {
          code: -2,
          message: '抽奖配置错误',
          data: null,
        };
      }

      const itemId = randomList[0];

      // 扣除金币
      const deductResult = await this.deductCharacterGold(characterId, needGold);
      if (!deductResult.success) {
        return {
          code: -3,
          message: '扣除金币失败',
          data: null,
        };
      }

      // 发放奖励
      await this.giveItemRewards(characterId, [itemId]);

      return {
        code: 0,
        message: '周末返场抽奖成功',
        data: {
          itemId,
          cost: needGold,
        },
      };

    } catch (error) {
      this.logger.error('周末返场抽奖失败', error);
      return {
        code: -4,
        message: '抽奖系统异常',
        data: null,
      };
    }
  }

  /**
   * 发放物品奖励
   * 调用Character服务的Inventory模块进行奖励发放
   */
  private async giveItemRewards(characterId: string, itemList: number[]): Promise<XResult<void>> {
    try {
      for (const itemId of itemList) {
        // 调用Character服务的Inventory模块添加物品到背包
        const result = await this.microserviceClient.call(
          MICROSERVICE_NAMES.CHARACTER_SERVICE,
          'inventory.addItemToBag',
          {
            characterId,
            bookMarkId: 1, // 默认背包页签
            itemId: itemId.toString(),
            serverId: 'server_001',
          }
        );

        if (result && result.code === 0) {
          this.logger.log(`发放物品奖励成功: ${characterId}, 物品ID: ${itemId}`);
        } else {
          this.logger.warn(`发放物品奖励失败: ${characterId}, 物品ID: ${itemId}, 错误: ${result?.message || '未知错误'}`);
        }
      }
    } catch (error) {
      this.logger.error('发放物品奖励失败', error);
    }
  }

  /**
   * 发放球员奖励
   * 集成Hero服务创建球员
   */
  private async giveHeroRewards(characterId: string, heroIdList: number[]): Promise<XResult<void>> {
    try {
      for (const heroId of heroIdList) {
        // 调用Hero服务创建球员
        const result = await this.microserviceClient.call(
          MICROSERVICE_NAMES.HERO_SERVICE,
          'hero.createHero',
          {
            characterId,
            heroConfigId: heroId,
            source: 'activity_lottery',
            serverId: 'server_001',
          }
        );

        if (result && result.code === 0) {
          this.logger.log(`发放球员奖励成功: ${characterId}, 球员ID: ${heroId}`);
        } else {
          this.logger.warn(`发放球员奖励失败: ${characterId}, 球员ID: ${heroId}, 错误: ${result?.message || '未知错误'}`);
        }
      }
    } catch (error) {
      this.logger.error('发放球员奖励失败', error);
    }
  }

  /**
   * 发放货币奖励
   * 集成Character服务增加货币
   */
  private async giveCurrencyRewards(characterId: string, currencyType: string, amount: number): Promise<XResult<void>> {
    try {
      // 调用Character服务增加货币
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.currency.add',
        {
          characterId,
          currencyDto: {
            currencyType,
            amount,
            reason: 'activity_lottery',
          },
          serverId: 'server_001',
        }
      );

      if (result && result.code === 0) {
        this.logger.log(`发放货币奖励成功: ${characterId}, 类型: ${currencyType}, 数量: ${amount}`);
      } else {
        this.logger.warn(`发放货币奖励失败: ${characterId}, 类型: ${currencyType}, 数量: ${amount}, 错误: ${result?.message || '未知错误'}`);
      }
    } catch (error) {
      this.logger.error('发放货币奖励失败', error);
    }
  }

  /**
   * 发放活动奖励
   * 基于old项目: 奖励发放逻辑
   */
  private async distributeActivityRewards(characterId: string, rewards: any[]): Promise<XResult<void>> {
    try {
      for (const reward of rewards) {
        switch (reward.itemType) {
          case 1: // 道具
            await this.microserviceClient.call(
              MICROSERVICE_NAMES.CHARACTER_SERVICE,
              'item.addItem',
              {
                characterId,
                resId: reward.resId,
                num: reward.num,
                reason: 'activity_reward'
              }
            );
            break;
          case 2: // 货币
            await this.microserviceClient.call(
              MICROSERVICE_NAMES.CHARACTER_SERVICE,
              'character.currency.add',
              {
                characterId,
                currencyDto: {
                  currencyType: reward.resId === 1 ? 'gold' : 'cash',
                  amount: reward.num,
                  reason: 'activity_reward'
                }
              }
            );
            break;
          case 3: // 球员
            await this.microserviceClient.call(
              MICROSERVICE_NAMES.HERO_SERVICE,
              'hero.addHero',
              {
                characterId,
                resId: reward.resId,
                reason: 'activity_reward'
              }
            );
            break;
        }
      }
    } catch (error) {
      this.logger.error('发放活动奖励失败', error);
    }
  }

  /**
   * 转换历史活动数据
   * 基于old项目: historyActList Map结构
   */
  private convertHistoryActivityData(historyList: any[]): Map<number, any> {
    const historyMap = new Map();
    if (historyList && Array.isArray(historyList)) {
      historyList.forEach(history => {
        historyMap.set(history.actId, {
          actId: history.actId,
          periods: history.periods,
          data: history.data,
          archiveTime: history.archiveTime,
        });
      });
    }
    return historyMap;
  }

  /**
   * 获取服务器ID
   * 从环境变量或配置获取
   */
  private getServerId(): string {
    return process.env.SERVER_ID || 'server_001';
  }

  /**
   * 检查老虎机免费次数
   * 基于old项目: 免费次数检查逻辑
   */
  private async checkTurntableFreeTimes(characterId: string, frequencyType: number): Promise<XResult<any>> {
    try {
      // TODO: 实现免费次数检查逻辑
      return { hasFreeTime: false, remainingTimes: 0 };
    } catch (error) {
      this.logger.error('检查老虎机免费次数失败', error);
      return { hasFreeTime: false, remainingTimes: 0 };
    }
  }

  /**
   * 扣除老虎机免费次数
   */
  private async deductTurntableFreeTimes(characterId: string, frequencyType: number, count: number): Promise<XResult<void>> {
    try {
      // TODO: 实现免费次数扣除逻辑
      this.logger.debug(`扣除老虎机免费次数: ${characterId}, 类型: ${frequencyType}, 次数: ${count}`);
    } catch (error) {
      this.logger.error('扣除老虎机免费次数失败', error);
    }
  }

  /**
   * 检查最佳11人保底
   * 基于old项目: 保底机制检查
   */
  private async checkBestFootballGuarantee(characterId: string): Promise<XResult<any>> {
    try {
      // TODO: 实现保底检查逻辑
      return { shouldTrigger: false, guaranteeType: 0 };
    } catch (error) {
      this.logger.error('检查最佳11人保底失败', error);
      return { shouldTrigger: false, guaranteeType: 0 };
    }
  }

  /**
   * 获取保底奖励
   */
  private async getGuaranteeRewards(guaranteeType: number): Promise<XResult<number[]>> {
    try {
      // TODO: 实现保底奖励获取逻辑
      return [1001]; // 默认保底奖励
    } catch (error) {
      this.logger.error('获取保底奖励失败', error);
      return [];
    }
  }

  /**
   * 重置最佳11人保底
   */
  private async resetBestFootballGuarantee(characterId: string): Promise<XResult<void>> {
    try {
      // TODO: 实现保底重置逻辑
      this.logger.debug(`重置最佳11人保底: ${characterId}`);
    } catch (error) {
      this.logger.error('重置最佳11人保底失败', error);
    }
  }

  /**
   * 检查拉霸免费次数
   */
  private async checkSlotsFreeTimes(characterId: string, frequencyType: number): Promise<XResult<any>> {
    try {
      // TODO: 实现拉霸免费次数检查逻辑
      return { hasFreeTime: false, remainingTimes: 0 };
    } catch (error) {
      this.logger.error('检查拉霸免费次数失败', error);
      return { hasFreeTime: false, remainingTimes: 0 };
    }
  }

  /**
   * 扣除拉霸免费次数
   */
  private async deductSlotsFreeTimes(characterId: string, frequencyType: number, count: number): Promise<XResult<void>> {
    try {
      // TODO: 实现拉霸免费次数扣除逻辑
      this.logger.debug(`扣除拉霸免费次数: ${characterId}, 类型: ${frequencyType}, 次数: ${count}`);
    } catch (error) {
      this.logger.error('扣除拉霸免费次数失败', error);
    }
  }

  /**
   * 权重随机选择
   * 基于old项目: randomBestFootball权重随机算法
   */
  private weightedRandomSelect(itemList: any[]): any {
    try {
      // 基于old项目randomBestFootball方法实现
      const randomList = [];

      // 构建权重数组（基于old项目逻辑）
      for (const item of itemList) {
        const weight = item.rate || item.weight || 1; // 使用rate字段作为权重
        for (let j = 0; j < weight; j++) {
          randomList.push(item);
        }
      }

      if (randomList.length === 0) {
        return null;
      }

      // 随机选择一个
      const randomIndex = Math.floor(Math.random() * randomList.length);
      return randomList[randomIndex];
    } catch (error) {
      this.logger.error('权重随机选择失败', error);
      return null;
    }
  }
}
