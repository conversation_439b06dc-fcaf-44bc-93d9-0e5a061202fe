/**
 * 增强版BaseRepository
 *
 * 核心功能：
 * - 继承原有BaseRepository的所有功能
 * - 提供Schema实例方法的便捷调用
 * - 自动适配Result模式
 * - 支持批量操作和条件查询
 *
 * 使用场景：
 * - 需要频繁使用Schema实例方法的Repository
 * - 需要复杂业务逻辑验证的场景
 * - 需要统一错误处理的场景
 */

import { Document, Model, FilterQuery, UpdateQuery } from 'mongoose';
import { BaseRepository } from './base-repository';
import { XResult, XResultUtils, RepositoryResultWrapper } from '../types/result.type';

/**
 * 增强版BaseRepository
 * 提供Schema实例方法的便捷调用和Result模式适配
 */
export abstract class EnhancedBaseRepository<T extends Document> extends BaseRepository<T> {
  
  constructor(model: Model<T>) {
    super(model);
  }

  // ==================== Schema方法调用包装器 ====================

  /**
   * 使用RepositoryResultWrapper包装Schema方法调用
   * 这是推荐的方式，利用现有的Result模式基础设施
   * @param id 文档ID
   * @param methodName 方法名
   * @param args 方法参数
   * @returns XResult<any>
   */
  async callSchemaMethod(
    id: string,
    methodName: string,
    ...args: any[]
  ): Promise<XResult<any>> {
    return await RepositoryResultWrapper.wrap(async () => {
      const doc = await this.model.findById(id);
      if (!doc) {
        throw new Error('文档不存在');
      }

      const method = (doc as any)[methodName];
      if (typeof method !== 'function') {
        throw new Error(`方法 ${methodName} 不存在`);
      }

      return method.apply(doc, args);
    });
  }

  /**
   * 批量调用Schema方法
   * @param filter 查询条件
   * @param methodName 方法名
   * @param args 方法参数
   * @returns XResult<SchemaMethodBatchResult[]>
   */
  async callSchemaMethodBatch(
    filter: FilterQuery<T>,
    methodName: string,
    ...args: any[]
  ): Promise<XResult<SchemaMethodBatchResult[]>> {
    return await RepositoryResultWrapper.wrap(async () => {
      const docs = await this.model.find(filter);
      const results: SchemaMethodBatchResult[] = [];

      for (const doc of docs) {
        const method = (doc as any)[methodName];
        if (typeof method === 'function') {
          try {
            const result = method.apply(doc, args);
            results.push({
              id: doc._id.toString(),
              success: true,
              data: result
            });
          } catch (error: any) {
            results.push({
              id: doc._id.toString(),
              success: false,
              error: error.message
            });
          }
        } else {
          results.push({
            id: doc._id.toString(),
            success: false,
            error: `方法 ${methodName} 不存在`
          });
        }
      }

      return results;
    });
  }

  /**
   * 条件调用Schema方法
   * 只对满足条件的文档调用方法
   * @param filter 查询条件
   * @param conditionMethod 条件检查方法名
   * @param targetMethod 目标方法名
   * @param args 方法参数
   * @returns XResult<SchemaMethodConditionalResult[]>
   */
  async callSchemaMethodConditional(
    filter: FilterQuery<T>,
    conditionMethod: string,
    targetMethod: string,
    ...args: any[]
  ): Promise<XResult<SchemaMethodConditionalResult[]>> {
    return await RepositoryResultWrapper.wrap(async () => {
      const docs = await this.model.find(filter);
      const results: SchemaMethodConditionalResult[] = [];

      for (const doc of docs) {
        const conditionFn = (doc as any)[conditionMethod];
        const targetFn = (doc as any)[targetMethod];

        if (typeof conditionFn !== 'function') {
          results.push({
            id: doc._id.toString(),
            executed: false,
            reason: `条件方法 ${conditionMethod} 不存在`
          });
          continue;
        }

        if (typeof targetFn !== 'function') {
          results.push({
            id: doc._id.toString(),
            executed: false,
            reason: `目标方法 ${targetMethod} 不存在`
          });
          continue;
        }

        try {
          const canExecute = conditionFn.apply(doc);
          if (canExecute) {
            const result = targetFn.apply(doc, args);
            results.push({
              id: doc._id.toString(),
              executed: true,
              data: result
            });
          } else {
            results.push({
              id: doc._id.toString(),
              executed: false,
              reason: '条件不满足'
            });
          }
        } catch (error: any) {
          results.push({
            id: doc._id.toString(),
            executed: false,
            reason: `执行失败: ${error.message}`
          });
        }
      }

      return results;
    });
  }

  // ==================== 常用Schema方法的便捷包装 ====================

  /**
   * 验证文档数据
   * 自动尝试调用常见的验证方法
   * @param id 文档ID
   * @returns XResult<ValidationResult>
   */
  async validateDocument(id: string): Promise<XResult<ValidationResult>> {
    return await RepositoryResultWrapper.wrap(async () => {
      const doc = await this.model.findById(id);
      if (!doc) {
        throw new Error('文档不存在');
      }

      // 尝试调用各种验证方法
      const validationMethods = [
        'validateData', 'validate', 'isValid',
        'validateGuildData', 'validateFriendData', 'validateMail'
      ];

      for (const methodName of validationMethods) {
        const method = (doc as any)[methodName];
        if (typeof method === 'function') {
          try {
            const result = method.apply(doc);
            return {
              isValid: result.isValid !== false,
              errors: result.errors || [],
              method: methodName,
              details: result
            };
          } catch (error: any) {
            // 继续尝试下一个方法
            continue;
          }
        }
      }

      throw new Error('未找到可用的验证方法');
    });
  }

  /**
   * 获取文档统计信息
   * 自动尝试调用常见的统计方法
   * @param id 文档ID
   * @returns XResult<any>
   */
  async getDocumentStats(id: string): Promise<XResult<any>> {
    return await RepositoryResultWrapper.wrap(async () => {
      const doc = await this.model.findById(id);
      if (!doc) {
        throw new Error('文档不存在');
      }

      // 尝试调用各种统计方法
      const statsMethods = [
        'getStats', 'getStatistics', 'getSummary',
        'getGuildStats', 'getFriendStats', 'getMailSummary',
        'getTournamentStats', 'getChannelStats'
      ];

      for (const methodName of statsMethods) {
        const method = (doc as any)[methodName];
        if (typeof method === 'function') {
          try {
            const result = method.apply(doc);
            return result;
          } catch (error: any) {
            // 继续尝试下一个方法
            continue;
          }
        }
      }

      throw new Error('未找到可用的统计方法');
    });
  }

  /**
   * 检查文档是否满足条件
   * 自动尝试调用常见的条件检查方法
   * @param id 文档ID
   * @param conditionType 条件类型
   * @returns XResult<boolean>
   */
  async checkDocumentCondition(id: string, conditionType: string): Promise<XResult<boolean>> {
    return await RepositoryResultWrapper.wrap(async () => {
      const doc = await this.model.findById(id);
      if (!doc) {
        throw new Error('文档不存在');
      }

      // 根据条件类型构建方法名
      const methodMappings: Record<string, string[]> = {
        'canAdd': ['canAddMember', 'canAddFriend', 'canAdd'],
        'canDelete': ['canBeDeleted', 'canDelete'],
        'canUpdate': ['canBeUpdated', 'canUpdate'],
        'canRead': ['canBeRead', 'canRead'],
        'canClaim': ['canBeClaimed', 'canClaim'],
        'isExpired': ['isExpired', 'isExpiringSoon'],
        'isValid': ['isValid', 'canUpgrade', 'canEvolve']
      };

      const methods = methodMappings[conditionType] || [conditionType];

      for (const methodName of methods) {
        const method = (doc as any)[methodName];
        if (typeof method === 'function') {
          try {
            const result = method.apply(doc);
            return Boolean(result);
          } catch (error: any) {
            // 继续尝试下一个方法
            continue;
          }
        }
      }

      throw new Error(`未找到条件检查方法: ${conditionType}`);
    });
  }

  /**
   * 转换文档为客户端数据
   * 自动尝试调用常见的转换方法
   * @param id 文档ID
   * @returns XResult<any>
   */
  async toClientData(id: string): Promise<XResult<any>> {
    return await RepositoryResultWrapper.wrap(async () => {
      const doc = await this.model.findById(id);
      if (!doc) {
        throw new Error('文档不存在');
      }

      // 尝试调用各种客户端数据转换方法
      const clientMethods = [
        'toClientData', 'toClient', 'toClientInfo',
        'toClientFriendData', 'toClientMailData', 'toClientGuildData',
        'toClientTournamentData', 'toClientSkillData'
      ];

      for (const methodName of clientMethods) {
        const method = (doc as any)[methodName];
        if (typeof method === 'function') {
          try {
            const result = method.apply(doc);
            return result;
          } catch (error: any) {
            // 继续尝试下一个方法
            continue;
          }
        }
      }

      // 如果没有专门的转换方法，返回基本的JSON格式
      return doc.toJSON();
    });
  }

  /**
   * 在文档上调用指定的实例方法（原版本）
   * @param id 文档ID
   * @param methodName 方法名
   * @param args 方法参数
   * @returns XResult<any>
   */
  async callDocumentMethod(
    id: string, 
    methodName: string, 
    ...args: any[]
  ): Promise<XResult<any>> {
    try {
      const doc = await this.model.findById(id);
      if (!doc) {
        return XResultUtils.error('文档不存在', 'DOCUMENT_NOT_FOUND');
      }

      const method = (doc as any)[methodName];
      
      if (typeof method !== 'function') {
        return XResultUtils.error(
          `方法 ${methodName} 不存在`, 
          'METHOD_NOT_FOUND'
        );
      }

      const result = method.apply(doc, args);
      return XResultUtils.ok(result);
      
    } catch (error: any) {
      return XResultUtils.error(
        `调用文档方法失败: ${error.message}`,
        'DOCUMENT_METHOD_CALL_ERROR',
        error
      );
    }
  }

  /**
   * 批量调用文档方法
   * @param filter 查询条件
   * @param methodName 方法名
   * @param args 方法参数
   * @returns XResult<any[]>
   */
  async callDocumentMethodBatch(
    filter: FilterQuery<T>,
    methodName: string,
    ...args: any[]
  ): Promise<XResult<any[]>> {
    try {
      const docs = await this.model.find(filter);
      const results: any[] = [];
      
      for (const doc of docs) {
        const method = (doc as any)[methodName];
        if (typeof method === 'function') {
          try {
            const result = method.apply(doc, args);
            results.push({
              id: doc._id,
              success: true,
              data: result
            });
          } catch (error: any) {
            results.push({
              id: doc._id,
              success: false,
              error: error.message
            });
          }
        }
      }
      
      return XResultUtils.ok(results);
      
    } catch (error: any) {
      return XResultUtils.error(
        `批量调用文档方法失败: ${error.message}`,
        'BATCH_DOCUMENT_METHOD_CALL_ERROR',
        error
      );
    }
  }

  /**
   * 条件调用文档方法
   * 只对满足条件的文档调用方法
   * @param filter 查询条件
   * @param conditionMethod 条件检查方法名
   * @param targetMethod 目标方法名
   * @param args 方法参数
   * @returns XResult<any[]>
   */
  async callDocumentMethodConditional(
    filter: FilterQuery<T>,
    conditionMethod: string,
    targetMethod: string,
    ...args: any[]
  ): Promise<XResult<any[]>> {
    try {
      const docs = await this.model.find(filter);
      const results: any[] = [];
      
      for (const doc of docs) {
        const conditionFn = (doc as any)[conditionMethod];
        const targetFn = (doc as any)[targetMethod];
        
        if (typeof conditionFn === 'function' && typeof targetFn === 'function') {
          try {
            const canExecute = conditionFn.apply(doc);
            if (canExecute) {
              const result = targetFn.apply(doc, args);
              results.push({
                id: doc._id,
                executed: true,
                data: result
              });
            } else {
              results.push({
                id: doc._id,
                executed: false,
                reason: 'Condition not met'
              });
            }
          } catch (error: any) {
            results.push({
              id: doc._id,
              executed: false,
              error: error.message
            });
          }
        }
      }
      
      return XResultUtils.ok(results);
      
    } catch (error: any) {
      return XResultUtils.error(
        `条件调用文档方法失败: ${error.message}`,
        'CONDITIONAL_DOCUMENT_METHOD_CALL_ERROR',
        error
      );
    }
  }

  /**
   * 验证文档数据
   * 调用文档的验证方法
   * @param id 文档ID
   * @returns XResult<ValidationResult>
   */
  async validateDocument(id: string): Promise<XResult<ValidationResult>> {
    try {
      const doc = await this.model.findById(id);
      if (!doc) {
        return XResultUtils.error('文档不存在', 'DOCUMENT_NOT_FOUND');
      }

      // 尝试调用各种验证方法
      const validationMethods = ['validateData', 'validate', 'isValid'];
      
      for (const methodName of validationMethods) {
        const method = (doc as any)[methodName];
        if (typeof method === 'function') {
          try {
            const result = method.apply(doc);
            return XResultUtils.ok({
              isValid: result.isValid !== false,
              errors: result.errors || [],
              method: methodName
            });
          } catch (error: any) {
            // 继续尝试下一个方法
            continue;
          }
        }
      }
      
      return XResultUtils.error('未找到可用的验证方法', 'NO_VALIDATION_METHOD');
      
    } catch (error: any) {
      return XResultUtils.error(
        `文档验证失败: ${error.message}`,
        'DOCUMENT_VALIDATION_ERROR',
        error
      );
    }
  }

  /**
   * 获取文档统计信息
   * 调用文档的统计方法
   * @param id 文档ID
   * @returns XResult<any>
   */
  async getDocumentStats(id: string): Promise<XResult<any>> {
    try {
      const doc = await this.model.findById(id);
      if (!doc) {
        return XResultUtils.error('文档不存在', 'DOCUMENT_NOT_FOUND');
      }

      // 尝试调用各种统计方法
      const statsMethods = ['getStats', 'getStatistics', 'getSummary'];
      
      for (const methodName of statsMethods) {
        const method = (doc as any)[methodName];
        if (typeof method === 'function') {
          try {
            const result = method.apply(doc);
            return XResultUtils.ok(result);
          } catch (error: any) {
            // 继续尝试下一个方法
            continue;
          }
        }
      }
      
      return XResultUtils.error('未找到可用的统计方法', 'NO_STATS_METHOD');
      
    } catch (error: any) {
      return XResultUtils.error(
        `获取文档统计失败: ${error.message}`,
        'DOCUMENT_STATS_ERROR',
        error
      );
    }
  }
}

// ==================== 接口定义 ====================

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  method: string;
  details?: any;
}

/**
 * Schema方法批量调用结果接口
 */
export interface SchemaMethodBatchResult {
  id: string;
  success: boolean;
  data?: any;
  error?: string;
}

/**
 * Schema方法条件调用结果接口
 */
export interface SchemaMethodConditionalResult {
  id: string;
  executed: boolean;
  data?: any;
  reason?: string;
}

/**
 * 文档方法调用结果接口（保持向后兼容）
 */
export interface DocumentMethodResult {
  id: string;
  success?: boolean;
  executed?: boolean;
  data?: any;
  error?: string;
  reason?: string;
}
